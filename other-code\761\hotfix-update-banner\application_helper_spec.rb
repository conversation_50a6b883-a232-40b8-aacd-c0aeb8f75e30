require_relative '../spec_helper'

describe ApplicationHelper, :nios => true do
  def mock_groups
    group1 = mock_model(DeviceGroup, :GroupName => 'one', :GroupID => 1, :device_count => 3)
    group2 = mock_model(DeviceGroup, :GroupName => 'two', :GroupID => 1, :device_count => 1)
    group3 = mock_model(DeviceGroup, :GroupName => 'three', :GroupID => 1, :device_count => 4)
    [group1, group2, group3]
  end
  before(:each) do
    @user = mock_model(AuthUser, :user_name => 'admin', :id => 1, :is_admin => true)
    @user.stub!(:get_privilege_device_groups).and_return((1..100).to_a)
    helper.stub!(:current_user).and_return(@user)
  end

  describe 'Title of available updates' do
    it 'should return correct string' do
      helper.parse_update_description('HotFix:1').should == "Update Available: 1 HotFix"
      helper.parse_update_description('HotFix:2').should == "Update Available: 2 HotFixes"
      helper.parse_update_description('Major:1').should == "Update Available: 1 Major Update"
      helper.parse_update_description('Major:2').should == "Update Available: 2 Major Updates"
    end
  end

  describe 'page title' do
    it 'should say NetMRI if the product is a netmri' do
      helper.stub!(:is_oc).and_return false
      helper.stub!(:sw_version).and_return '1'
      helper.page_title.should === "#{Brand.lookup(:COMPANY_NAME)} #{Brand.lookup(:PRODUCT_NAME)} - 1"
    end

    it 'should say Operations Center if the product is an OC' do
      helper.stub!(:is_oc).and_return true
      helper.stub!(:sw_version).and_return '1'
      helper.page_title.should === "#{Brand.lookup(:COMPANY_NAME)} #{Brand.lookup(:PRODUCT_NAME_OC)} - 1"
    end
  end

  describe 'sw_version' do
    it 'should return Unknown if no Version was found' do
      g = GlobalSetting.new
      g.stub!(:server_cfg).and_return Hash.new
      GlobalSetting.stub!(:new).and_return g

      helper.sw_version.should === 'Unknown'
    end
  end

  describe 'compute_exact_time_period' do
    it 'should make 2010-01-01/Daily -> 2010-01-01 00:00:00 - 2010-01-02 00:00:00' do
      x = helper.compute_exact_time_period '2010-01-01', 'Daily'
      x[0].should == Time.parse('2010-01-01')
      x[1].should == Time.parse('2010-01-02')
    end

    it 'should make 2010-01-01/Weekly -> 2009-12-27 00:00:00 - 2010-01-03 00:00:00' do
      x = helper.compute_exact_time_period '2010-01-01', 'Weekly'
      x[0].should == Time.parse('2009-12-27')
      x[1].should == Time.parse('2010-01-03')
    end

    it 'should make 2010-01-01/Monthly -> 2009-01-01 00:00:00 - 2010-02-01 00:00:00' do
      x = helper.compute_exact_time_period '2010-01-01', 'Monthly'
      x[0].should == Time.parse('2010-01-01')
      x[1].should == Time.parse('2010-02-01')
    end

    it 'should make 2010-01-01/7-Day -> 2009-12-26 00:00:00 - 2010-01-02 00:00:00' do
      x = helper.compute_exact_time_period '2010-01-01', '7-Day'
      x[0].should == Time.parse('2009-12-26')
      x[1].should == Time.parse('2010-01-02')
    end

    it 'should make 2010-01-01/30-Day -> 2009-12-3 00:00:00 - 2010-01-02 00:00:00' do
      x = helper.compute_exact_time_period '2010-01-01', '30-Day'
      x[0].should == Time.parse('2009-12-03')
      x[1].should == Time.parse('2010-01-02')
    end
  end
end
