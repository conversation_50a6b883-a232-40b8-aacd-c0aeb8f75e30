#!/usr/bin/perl
######################################################################
#
#  File:    DNS.pm
#
#  Purpose: library of functions used by DnsProcessor
#
#  Notes:   1) These routines are centralized to allow cleaner
#              unit testing.
#           2) currently, this is just standard functions, it's not
#              Object Oriented at this point
#
#####################################################################

package NetMRI::DNS;

require 5.000;
use strict;

# Ideally, we'd only read one time each time through, but for now, to keep the data
# current, we have 2 different readers of the DNS resolv.conf file.

sub getDNSServers {
  my $log = shift;
  my $resolvFileName = shift;

  my @retList = ();

  # if passed in, we have one for unit testing. Else, use the master
  # in /etc.
  if ($resolvFileName eq '') {
    $resolvFileName = '/etc/resolv.conf';
  }

  # we need an error handler
  if ( open RESOLVE_FILE, "<", $resolvFileName ) {
    while (my $line=<RESOLVE_FILE>) {
      chomp $line;
      if ($line =~ /^nameserver (.*)$/) {
	push (@retList, $1);
      }
    }
  } else {
    $log->error("Cannot open |$resolvFileName|");
  }

  return @retList;

}

sub getDNSDomains {
  my $log = shift;
  my $resolvFileName = shift;

  my @retList = ();

  # if passed in, we have one for unit testing. Else, use the master
  # in /etc.
  if ($resolvFileName eq '') {
    $resolvFileName = '/etc/resolv.conf';
  }

  # we need an error handler
  if ( open RESOLVE_FILE, "<", $resolvFileName ) {
    while (my $line=<RESOLVE_FILE>) {
      chomp $line;
      if ($line =~ /^search (.*)$/) {
	# there can only be 1 search line, so we find it, we're done
	# we return it as an array
	return split(' ',$1);
      }
    }
  } else {
    $log->error("Cannot open |$resolvFileName|");
  }

  return @retList;		# shoudl be empty list

}

1;
