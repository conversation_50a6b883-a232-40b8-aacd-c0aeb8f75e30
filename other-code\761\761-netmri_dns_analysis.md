# NetMRI DNS Resolution Analysis & Onboarding Guide

## Executive Summary

This document provides an in-depth analysis of how DNS resolution works in Infoblox NetMRI version 7.6.1, with focus on interface-specific DNS servers, VRF/Network View interactions, and the complete DNS resolution workflow.

## Key Findings from Code Analysis

### 🚨 **Critical Gap Identified**
The current code implementation does **NOT** support per-interface DNS servers as expected. The DNS resolution process uses a global DNS server configuration that applies to all devices regardless of which interface discovered them.

---

## 1. DNS Resolution Workflow Overview

### Current Implementation Flow
```
1. Process starts every 30 minutes (1800 seconds)
2. Fork child process for DNS operations
3. Query devices needing DNS resolution
4. Use GLOBAL DNS servers for ALL devices
5. Perform reverse DNS lookups (PTR records)
6. Update device names based on priority
7. Raise issues for DNS mismatches
```

### Key Configuration Settings
- **DnsLookUpOptions**: Controls DNS resolution behavior
  - `"OFF"`: No DNS resolution
  - `"INFRAONLY"`: Only infrastructure devices
  - `"ALL"`: All devices
- **NamePriority**: Determines name source priority
  - `"SNMP"`: User > SNMP > DNS > FingerPrint > Call Server
  - `"DNS"`: User > DNS > FingerPrint > SNMP > Call Server
- **dnsThreads**: Number of parallel DNS workers (default: 1)
- **DnsLookUpThrottle**: Throttling percentage for DNS queries

---

## 2A. DNS Server Selection Deep Dive

### **getAllDNSServers() Function Analysis - SOLVED**

**Source Code** (from DNS.pm):
```perl
sub getAllDNSServers {
  my $interface_config = getAllInterfaceDetails();
  my %retList;
  foreach (keys %{ $interface_config }) {
    $retList{$interface_config->{$_}->{primary_dns_server}} = 1 if $interface_config->{$_}->{primary_dns_server};
    $retList{$interface_config->{$_}->{secondary_dns_server}} = 1 if $interface_config->{$_}->{secondary_dns_server};
  }
  return keys %retList;
}
```

**Critical Discovery**:
- **Data Source**: `getAllInterfaceDetails()` from `NetMRI::Util::HW` module
- **Aggregation Logic**: Collects ALL DNS servers from ALL interfaces
- **Deduplication**: Uses hash to eliminate duplicates (`%retList`)
- **Returns**: Unique list of ALL DNS servers across ALL interfaces
- **No Interface Context**: Interface association is LOST in the aggregation process

### **DNS Server Usage Pattern**

```perl
# Pattern used in TWO places in the code:

# 1. Forward DNS Resolution (A/AAAA records) - Line 491
foreach (@dnsServers) {
    $log->debug("Query: Name ['$sysName'] IP ['$ip'] Type ['$qType'] DNS ['$_']") if $debug;
    $dnsRes->nameserver($_);
    $query = $dnsRes->search($sysName, $qType);
    last if $query;  # STOPS ON FIRST SUCCESS
    $log->debug("Failed with NS response. " . $dnsRes->errorstring) if $debug;
}

# 2. Reverse DNS Resolution (PTR records) - Line 273
foreach (@dnsServers) {
    $log->debug("Query: IP [$devIP] Server [$_]") if $debug;
    $dnsRes->nameserver($_);
    $query = $dnsRes->send($devIP);
    last if $query;  # STOPS ON FIRST SUCCESS
    $log->debug("Failed with NS response. " . $dnsRes->errorstring) if $debug;
}
```

### **Consistent Behavior Across Both DNS Operations**:
1. **Sequential Processing**: Tries DNS servers in array order
2. **First Success Rule**: Stops immediately on first successful response
3. **Failure Tolerance**: Continues to next DNS server on failure
4. **Complete Failure**: If all DNS servers fail, `$query` remains undefined

### **Questions for Database Investigation**:
1. **Storage Schema**: How are DNS servers stored in NetMRI database?
2. **Interface Mapping**: Is there a table linking DNS servers to specific interfaces?
3. **Priority/Order**: How is the order of DNS servers determined?
4. **Configuration UI**: Where do admins configure per-interface DNS servers?

### **getAllSearchDomains() Function Analysis - SOLVED**

**Source Code** (from DNS.pm):
```perl
sub getAllSearchDomains {
  my $interface_config = getAllInterfaceDetails();
  my %retList;
  foreach (keys %{ $interface_config }) {
    foreach (@{getPerInterfaceSearchDomain($interface_config->{$_})}) {
      next if $_ eq "";
      $retList{$_} = 1;
    }
  }
  return sort {length($b) <=> length($a)} keys %retList;
}

sub getPerInterfaceSearchDomain {
    my $interface = shift;
    if (ref($interface->{search_domains}) eq 'ARRAY'){
        return $interface->{search_domains};
    } elsif (defined($interface->{search_domains})) {
        return [split(/,/, $interface->{search_domains})];
    } else {
        return [];
    }
}
```

**Critical Discovery**:
- **Data Source**: Same `getAllInterfaceDetails()` function
- **Aggregation Logic**: Collects ALL search domains from ALL interfaces
- **Deduplication**: Uses hash to eliminate duplicates
- **Sorting**: Returns domains sorted by length (longest first)
- **Interface Context Lost**: No way to determine which interface a search domain belongs to

### Current Implementation (Lines 38-39)
```perl
my @dnsServers = getAllDNSServers();
my @searchDomains = getAllSearchDomains();
```

### **Analysis**: getAllDNSServers() Function
**Source**: The function is imported from `NetMRI::DNS` module (line 17)
```perl
use NetMRI::DNS qw(
  getAllDNSServers
  getAllSearchDomains
);
```

**Key Questions**:
- **Database Source**: Unknown - function implementation not visible in current code
- **Return Format**: Returns an array `@dnsServers` 
- **Content**: Likely contains all DNS servers configured across ALL interfaces
- **Order**: Unknown - depends on database query or configuration order

### **Expected Behavior vs Current Reality**

| Expected | Current Reality |
|----------|----------------|
| Each interface has its own DNS servers | Single global DNS server list |
| Devices use DNS servers from their discovery interface | All devices use same DNS servers |
| Fallback to management interface DNS if scan interface has none | No fallback mechanism visible |

---

## 3. VRF and Network View Integration

### Current Code Limitations
The code shows **NO evidence** of VRF or Network View awareness in DNS resolution:

1. **Device Selection** (Lines 175-192): Uses generic device queries without VRF/Network View filtering
2. **DNS Server Selection**: No logic to map devices to interface-specific DNS servers
3. **Search Domain Handling**: Global search domains, not per-Network View

### Missing Components
- No VRF-to-Network View mapping consideration
- No interface-to-device mapping for DNS server selection
- No Network View-specific search domain handling

---

## 4. Detailed Code Analysis

### 4.1 DNS Resolution Process (`updateDnsNames()` function)

#### Device Selection Logic
```perl
if ($config->{DnsLookUpOptions} eq "INFRAONLY") {
    $sql->execute ("create temporary table dnsDeviceTable as "
                   . " select DeviceID, DeviceIPDotted as IPAddress, DeviceType like 'SDN%' as fromSdn "
                   . " from   report.InfraDevice ");
} elsif($config->{DnsLookUpOptions} eq "ALL") {
    $sql->execute ("create temporary table dnsDeviceTable as "
                   . " select DeviceID, IPAddress, Type like 'SDN%' as fromSdn"
                   . " from   netmri.Device ");
}
```

**Analysis**: 
- Selects devices based on configuration but ignores interface/VRF context
- No interface information captured for DNS server selection

#### DNS Server Selection Logic (Lines 273-327)
```perl
# DNS Resolution Logic - CRITICAL ANALYSIS
foreach (@dnsServers) {
    $log->debug("Query: IP [$devIP] Server [$_]") if $debug;
    $dnsRes->nameserver($_);
    $query = $dnsRes->send($devIP);
    last if $query;  # ← STOPS ON FIRST SUCCESS
    $log->debug("Failed with NS response. " . $dnsRes->errorstring) if $debug;
}
```

### **DNS Server Usage Logic - DETAILED BREAKDOWN**:

1. **Iteration Order**: Uses DNS servers in the order returned by `getAllDNSServers()`
2. **First Success Wins**: `last if $query;` - **STOPS** on first successful response
3. **Fallback Strategy**: Only tries next DNS server if current one fails
4. **No Load Balancing**: All devices start with the same first DNS server
5. **Failure Handling**: Logs error and tries next server in array

### **Critical Behavior**:
- **If DNS Server #1 responds**: Never tries DNS Server #2, #3, etc.
- **If DNS Server #1 fails**: Tries DNS Server #2
- **If DNS Server #2 fails**: Tries DNS Server #3
- **And so on...**

### **Performance Implications**:
- **First DNS server gets ALL traffic** (if responsive)
- **Subsequent servers act as fallbacks only**
- **No load distribution** across multiple DNS servers
- **Single point of failure** if first DNS server is slow but responsive

### 4.2 Name Priority and Update Logic (`updateDeviceNames()` function)

#### Name Priority Implementation (Lines 393-408)
```perl
if ($$config{NamePriority} eq 'SNMP') {
    $name = $deviceNames{$device}->{User}
         || $deviceNames{$device}->{SNMP}
         || $deviceNames{$device}->{DNS}
         || $deviceNames{$device}->{FingerPrint}
         || $deviceNames{$device}->{'Call Server'}
         || "unknown";
} else {
    $name = $deviceNames{$device}->{User}
         || $deviceNames{$device}->{DNS}
         || $deviceNames{$device}->{FingerPrint}
         || $deviceNames{$device}->{SNMP}
         || $deviceNames{$device}->{'Call Server'}
         || "unknown";
}
```

**Analysis**: Proper name priority logic exists but operates on global DNS results.

### 4.3 Search Domain Handling (Lines 410-416)
```perl
for my $strip_domain (@searchDomains) {
    $strip_domain = ".$strip_domain" if substr($strip_domain, 0, 1) ne ".";
    $log->debug("Check domain: '$strip_domain'");
    $name =~ s/\Q$strip_domain\E$//i;
}
```

**Analysis**: Domain stripping uses global search domains, not Network View-specific ones.

---

## 5. Performance and Scalability Analysis

### Threading and Load Distribution
- **Multi-threading**: Supports parallel DNS workers via `dnsThreads` configuration
- **Batch Processing**: Divides devices among worker threads
- **Throttling**: Implements CPU-based throttling algorithm

### Throttling Algorithm (Lines 75-105)
```perl
sub calculateThrottleSeconds {
    my $uptime = `/bin/cat /proc/uptime`;
    # Complex algorithm based on system capacity and network throughput
    # Calculates sleep time between DNS queries
}
```

**Analysis**: Sophisticated throttling but may not account for per-interface network capacity.

---

## 6. Critical Issues and Gaps - UPDATED WITH ROOT CAUSE

### 6.1 Interface-Specific DNS Resolution - ROOT CAUSE IDENTIFIED
**Impact**: High - Core functionality expectation not met
**Root Cause**: The `getAllDNSServers()` function **deliberately aggregates** DNS servers from all interfaces, destroying interface-specific context.

**Technical Details**:
- DNS servers ARE configured per interface (primary_dns_server, secondary_dns_server)
- The aggregation process in DNS.pm removes interface association
- `DnsProcessor.pl` receives a flat list without interface context
- No mechanism exists to map devices back to their discovery interface

**Code Evidence**:
```perl
# DNS.pm aggregates ALL DNS servers from ALL interfaces
foreach (keys %{ $interface_config }) {
    $retList{$interface_config->{$_}->{primary_dns_server}} = 1 if $interface_config->{$_}->{primary_dns_server};
    $retList{$interface_config->{$_}->{secondary_dns_server}} = 1 if $interface_config->{$_}->{secondary_dns_server};
}
return keys %retList;  # ← Interface context LOST here
```

### 6.2 DNS Server Order and Priority - CLARIFIED
**Impact**: Medium - Unpredictable DNS server selection
**Root Cause**: Hash key ordering is unpredictable in Perl
- `return keys %retList;` returns DNS servers in **random hash order**
- No interface-specific priority (primary vs secondary)
- No management interface fallback priority

### 6.3 Search Domain Aggregation - SIMILAR ISSUE
**Impact**: Medium - Domain stripping not Network View-specific
**Root Cause**: Same aggregation pattern as DNS servers
- Search domains are collected from ALL interfaces
- Sorted by length (longest first) but no interface context
- No Network View-specific domain handling

---

## 7. Recommendations for Implementation - UPDATED

### 7.1 Interface-Specific DNS Server Support - DETAILED SOLUTION
**Current Issue**: DNS servers are aggregated, losing interface context
**Solution Requirements**:
1. **Preserve Interface Context**: Modify DNS.pm to return interface-to-DNS server mappings
2. **Device-Interface Mapping**: Add interface information to device queries in DnsProcessor.pl
3. **DNS Server Selection**: Select DNS servers based on device's discovery interface

**Proposed Code Changes**:
```perl
# DNS.pm - New function to preserve interface context
sub getInterfaceDNSServers {
    my $interface_config = getAllInterfaceDetails();
    my %interface_dns = ();
    
    foreach my $interface (keys %{ $interface_config }) {
        my @dns_servers = ();
        push @dns_servers, $interface_config->{$interface}->{primary_dns_server} 
            if $interface_config->{$interface}->{primary_dns_server};
        push @dns_servers, $interface_config->{$interface}->{secondary_dns_server} 
            if $interface_config->{$interface}->{secondary_dns_server};
        $interface_dns{$interface} = \@dns_servers if @dns_servers;
    }
    
    return %interface_dns;
}

# DnsProcessor.pl - Modified device query to include interface info
select d.DeviceID, d.IPAddress, d.fromSdn, i.InterfaceName
from   dnsDeviceTable d
       left join DeviceInterfaceMapping i on d.DeviceID = i.DeviceID
```

### 7.2 VRF/Network View Integration - COMPREHENSIVE APPROACH
**Current Issue**: No VRF/Network View awareness
**Solution Requirements**:
1. **Network View Context**: Include Network View information in device selection
2. **Interface-Network View Mapping**: Map devices to Network Views through interfaces
3. **Search Domain Segmentation**: Apply Network View-specific search domains

### 7.3 DNS Server Priority and Fallback - CLEAR LOGIC
**Current Issue**: Random DNS server order from hash keys
**Solution Requirements**:
1. **Primary/Secondary Priority**: Maintain interface-specific DNS server order
2. **Management Interface Fallback**: Use management interface DNS when scan interface has none
3. **Consistent Ordering**: Ensure predictable DNS server selection

### 7.4 Database Schema Requirements
**Missing Tables/Columns**:
1. **DeviceInterfaceMapping**: Link devices to their discovery interfaces
2. **InterfaceNetworkView**: Map interfaces to Network Views
3. **NetworkViewSearchDomains**: Per-Network View search domain configuration

---

## 8. Questions for NetMRI 7.6.0 Comparison

When analyzing NetMRI 7.6.0, focus on:

1. **Interface Mapping**: Does 7.6.0 include interface information in device queries?
2. **DNS Server Selection**: Any evidence of per-interface DNS server logic?
3. **VRF Integration**: VRF/Network View awareness in DNS resolution?
4. **Search Domain Handling**: Per-Network View search domain support?
5. **Database Schema**: Different table structures for device-interface relationships?

---

## 9. Conclusion

The current NetMRI 7.6.1 DNS resolution implementation provides basic functionality but lacks the expected interface-specific DNS server support and VRF/Network View integration. The system operates with a global DNS configuration that doesn't align with the architectural expectations of per-interface DNS servers and network segmentation.

**Key Action Items**:
1. Analyze NetMRI 7.6.0 to identify differences in DNS resolution approach
2. Determine if interface-specific DNS servers were supported in previous versions
3. Identify database schema requirements for proper interface-device mapping
4. Plan implementation strategy for VRF/Network View-aware DNS resolution

---

## 10. Technical Deep Dive - Code Sections

### Function: `updateDnsNames()`
- **Purpose**: Main DNS resolution function
- **Lines**: 157-364
- **Key Issue**: No interface-specific DNS server selection

### Function: `updateDeviceNames()`
- **Purpose**: Updates device names based on priority
- **Lines**: 367-431
- **Key Issue**: Uses global search domains

### Function: `raiseIssues()`
- **Purpose**: Validates DNS name consistency
- **Lines**: 433-539
- **Key Issue**: No Network View context in validation

### Function: `calculateThrottleSeconds()`
- **Purpose**: Performance throttling
- **Lines**: 75-105
- **Key Issue**: Global throttling, no per-interface consideration