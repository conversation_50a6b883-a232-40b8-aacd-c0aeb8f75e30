#!/usr/bin/perl
######################################################################
#
#  File:    DNS.pm
#
#  Purpose: library of functions used by DnsProcessor
#
#  Notes:   1) These routines are centralized to allow cleaner
#              unit testing.
#           2) currently, this is just standard functions, it's not
#              Object Oriented at this point
#
#####################################################################

package NetMRI::DNS;

require 5.30.0;
use strict;
use warnings;

use NetMRI::Util::HW qw(
  getAllInterfaceDetails
);

use base qw(Exporter);

our @EXPORT_OK = qw(
  getAllDNSServers
  getAllSearchDomains
  getPerInterfaceSearchDomain
);

sub getAllDNSServers {
  my $interface_config = getAllInterfaceDetails();
  my %retList;
  foreach (keys %{ $interface_config }) {
    $retList{$interface_config->{$_}->{primary_dns_server}} = 1 if $interface_config->{$_}->{primary_dns_server};
    $retList{$interface_config->{$_}->{secondary_dns_server}} = 1 if $interface_config->{$_}->{secondary_dns_server};
  }
  return keys %retList;
}

sub getAllSearchDomains {
  my $interface_config = getAllInterfaceDetails();
  my %retList;
  foreach (keys %{ $interface_config }) {
    foreach (@{getPerInterfaceSearchDomain($interface_config->{$_})}) {
      next if $_ eq "";
      $retList{$_} = 1;
    }
  }
  return sort {length($b) <=> length($a)} keys %retList;
}

sub getPerInterfaceSearchDomain {
    my $interface = shift;
    if (ref($interface->{search_domains}) eq 'ARRAY'){
        return $interface->{search_domains};
    } elsif (defined($interface->{search_domains})) {
        return [split(/,/, $interface->{search_domains})];
    } else {
        return [];
    }
}

1;
