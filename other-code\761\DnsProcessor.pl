#!/usr/bin/perl

use strict;
use warnings;

use Net::DNS;
use Net::IPv6Addr;
use POSIX ":sys_wait_h";
use NetMRI::Logger;
use NetMRI::Analysis::IssueClient;
use NetMRI::Util::Process;
use NetMRI::Config;
use NetMRI::Sql;
use NetMRI::DNS qw(
  getAllDNSServers
  getAllSearchDomains
);

NetMRI::Util::Process::runAsNetMRIUser();

my $skipjack = "/tools/skipjack";

my $debug = (-e '/tmp/DNS.debug');

my $log4perlCategory = 'NetMRI.Discovery';
my $log = NetMRI::Logger->new(
  Log => "$skipjack/logs/DNS.log",
  LogOptions => {
    Append           => 1,
    RedirSTDERR      => 0,
    RedirSTDOUT      => $debug,
    Log4perlCategory => $log4perlCategory,
    Owner            => "netmri.users"
  }
);

#-----------------------------------------------------------------------------
#     Run at a lower priority
#-----------------------------------------------------------------------------
setpriority(0, $$, 10);

my $config;
my $sql;
my $lastRun = time() - 3600;

my $sleep = ($debug ? 120 : 1800); # Speed things up if debugging
$log->setDebug(1) if $debug;
$log->{MessageTag} = "[$$]";

my $numCores = `/bin/grep '^processor' /proc/cpuinfo | /usr/bin/wc -l`;
chop $numCores;

my @dnsServers = getAllDNSServers();
my @searchDomains = getAllSearchDomains();

$log->info("DNS Processor Startup");

while (1) {
  $log->debug("Sleeping $sleep seconds...") if $debug;
  sleep $sleep;

  if (fork() == 0) {
    $log->{MessageTag} = "[$$]";
    # be sure to never use SkipAdvanced => 1 here, we need Advanced Settings for 'NamePriority'
    $config = NetMRI::Config->new();
    $$config{PlatformInterfaceLimit} ||= 5000;
    $$config{dnsThreads}             ||= 1;

    # In case settings were changed, re-read them
    @dnsServers = getAllDNSServers();
    @searchDomains = getAllSearchDomains();
    $log->debug("DNS Servers: " . join(", ", @dnsServers)) if $debug;
    $log->debug("Search Domains: " . join(", ", @searchDomains)) if $debug;


    updateDnsNames();

    $sql = NetMRI::Sql->new(
      DBType          => "mysql",
      DBName          => "netmri",
      DBUser          => "root",
      Config          => $config,
      ContinueOnError => 1
    );
    abort("Error connecting to database. " . $sql->errormsg()) if $sql->errormsg();

    updateDeviceNames();

    raiseIssues();

    undef $sql;
    exit 0;
  }
  while (wait() != -1) { }

  $lastRun = time();
}

exit 0;

#----------------------------------------------------------------------

# Calculate a number of seconds to wait based on:
# - the average number of bytes transmitted over the uptime of the computer
# - spread across the number of cores as being max capacity rate of the
# - configured scan interface.
#
sub calculateThrottleSeconds {
  my $uptime = `/bin/cat /proc/uptime`;
  chop $uptime;
  my @uptimeArr = split(/ /, $uptime);

  my $percIdle     = abs(((int($uptimeArr[1]) / $numCores) / int($uptimeArr[0])));
  # we made requests from mgmt interface (eth0)
  my $trxBytes     = int(`/bin/cat /sys/class/net/eth0/statistics/tx_bytes`);
  my $maxCapPerSec = abs($trxBytes / int($uptimeArr[1]));
  my $capacity     = abs(($maxCapPerSec * $percIdle) / int($config->{dnsThreads}));
  my $calculated   = abs($capacity * ($config->{DnsLookUpThrottle} / 100));
  my $throttle     = int($capacity - $calculated);

  $log->debug( <<INFO
***  capacity debug values are  ***
\tUptime: $uptime
\tUTA cnt: @uptimeArr
\tNumCores: $numCores
\tUTA[1]: $uptimeArr[1]
\tMaxCapPerSec: $maxCapPerSec
\tTrxBytes: $trxBytes
\tCapacity: $capacity
\tCalculated: $calculated
\tThrottle: $throttle
\t***                             ***
INFO
  );
  return $throttle;

}

sub updateDnsNames {
  if ($config->{DnsLookUpOptions} eq "OFF") {
    $log->info("DNS Processor off, not collecting reverse DNS names");
    return;
  }
  $sql = NetMRI::Sql->new(
    DBType          => "mysql",
    DBName          => "netmri",
    DBUser          => "root",
    Config          => $config,
    ContinueOnError => 1
  );
  if ($sql->errormsg()) {
    abort("Error connecting to database.  " . $sql->errormsg());
  }

  # The base table needs to only use network devices in Augusta
  $sql->execute("use netmri");
  $sql->execute("drop temporary table if exists dnsDeviceTable");

	if ($config->{DnsLookUpOptions} eq "INFRAONLY") {
	    $sql->execute ("create temporary table dnsDeviceTable as "
			   . " select DeviceID, DeviceIPDotted as IPAddress, DeviceType like 'SDN%' as fromSdn "
			   . " from   report.InfraDevice ");
	    $log->debug("Created dnsDeviceTable as AutomationGridMember");
	} elsif($config->{DnsLookUpOptions} eq "ALL") {
	    $sql->execute ("create temporary table dnsDeviceTable as "
			   . " select DeviceID, IPAddress, Type like 'SDN%' as fromSdn"
			   . " from   netmri.Device ");
	    $log->debug("Created dnsDeviceTable as NetMRI");
	}

  my @devices = $sql->table("
			drop temporary table if exists dnsNameProps;

			create temporary table dnsNameProps as
			select DeviceID, Value as DNSName, unix_timestamp(Timestamp) as LastTime
			from   netmri.DeviceProperty
			where  PropertyName = 'sysName'
			and    Source = 'DNS';

			alter table dnsNameProps add index(DeviceID);

			select \@timeCutOff := unix_timestamp(now() - interval 12 hour);

			select d.DeviceID, d.IPAddress, d.fromSdn
			from   dnsDeviceTable d
			       left outer join dnsNameProps p on d.DeviceID = p.DeviceID
			where  p.LastTime is null or p.LastTime < \@timeCutOff;

			drop temporary table if exists dnsDeviceTable;
	", AllowNoRows => 1);

  my @devices_with_dns = $sql->table("
    select DeviceID from dnsNameProps p;
    drop temporary table if exists dnsNameProps; ",
    AllowNoRows => 1
  );

  my %has_dns = map {$_->{DeviceID} => 1} @devices_with_dns;
  undef @devices_with_dns;

  undef $sql;    # Close SQL connection

  my $numToFork = int($$config{dnsThreads});

  my $batchSize = int(scalar(@devices) / $numToFork);

  $log->info("Processing " . scalar(@devices) . " devices.");

  for (my $i = 0; $i < $numToFork; $i++) {
    if (fork() == 0) {

      $log->{MessageTag} = "[$$]";
      ## Reopen sqlConnection here since each child needs to have
      ## they're own connection open otherwise "MySQL server has
      ## gone away" error messages may occur.

      $sql = NetMRI::Sql->new(
        DBType          => "mysql",
        DBName          => "netmri",
        DBUser          => "root",
        Config          => $config,
        ContinueOnError => 1
      );
      if ($sql->errormsg()) {
        abort("Error connecting to database.  " . $sql->errormsg());
      }

      my $start = 0;
      my $end   = scalar(@devices);

      if ($i > 0) {
        $start = $batchSize * $i;
      }
      if ($i + 1 < $numToFork) {
        $end = $start + $batchSize;
      }

      my $startWork = time();

      $log->info("Worker " . ($i + 1) . " processing " . ($end - $start) . " devices.");

      my $dnsRes = Net::DNS::Resolver->new;
      $dnsRes->persistent_udp(1);
      $dnsRes->udp_timeout(2);
      $dnsRes->retry(1);
      $dnsRes->retrans(1);

      for (my $j = $start; $j < $end; $j++) {
        my $devIP    = $devices[$j]->{IPAddress};
        my $deviceID = $devices[$j]->{DeviceID};

        # NIOS-72372: do not resolve SDN devices
        # since we already have suitable device name collected from SDN controller
        if ($devices[$j]->{fromSdn} eq '1') {
          $log->debug("Skipping SDN device [$devIP], id: $deviceID") if $debug;
          next;
        }

				$log->debug("Processing device [$devIP], id: $deviceID") if $debug;

        # RFE-6541; we have implemented throttling in terms of a capacity vs time algorithm
        # we attempt to look at the rate of bytes exiting the system and determine over the
        # uptime of the unit, what the distributed capacity is based on seconds. We then
        # throttle according to the users wishes.
        sleep(calculateThrottleSeconds());

        my $name  = "";

        my $query;
        # Trying with each DNS server until we get a response or run out of servers
        # If we put dns servers array in the nameserver method, it will return undef
        # in case of NXDOMAIN, but another server may have the answer.
        foreach (@dnsServers) {
          $log->debug("Query: IP [$devIP] Server [$_]") if $debug;
          $dnsRes->nameserver($_);
          $query = $dnsRes->send($devIP);
          last if $query;
          $log->debug("Failed with NS response. " . $dnsRes->errorstring) if $debug;
        }

        # NOTE: The ptr record returned will depend
        # on the IP address looked up. If v6, will
        # come from the ip6.arpa. domain, if v4 will
        # come from in-addr.arpa. as before. So when
        # testing different address families, make sure
        # appropriate reverse domains/zones are setup.
        if ($query) {
          foreach my $rr ($query->answer) {
            $log->debug("Found " . $rr->type . ' record.') if $debug;
            next if ($rr->type ne "PTR");
            $name = $rr->ptrdname;
          }

          my $sqlString;
          if ($name) {
            $log->debug("PTR Name update: " . "IP [$devIP] Name [$name]") if $debug;
            $sqlString = "replace netmri.DeviceProperty
							(DeviceID, PropertyName, PropertyIndex, Source, Value, Timestamp)
							values
							($deviceID, 'sysName', '', 'DNS', " . $sql->escape($name) . ", now())
						";
          } elsif ($has_dns{$deviceID}) {
            $log->debug("IP [$devIP] does not resolve, removing") if $debug;
            $sqlString =
              "delete from netmri.DeviceProperty where DeviceID = $deviceID and PropertyName = 'sysName' and Source = 'DNS'";
            $sqlString .= '_' . $$ . '_';
            $sqlString .=
              "update netmri.DeviceProperty set Timestamp = now() where DeviceID = $deviceID and PropertyName = 'sysName'";
          }
          $sql->setDelimiter("_" . $$ . "_");
          $sql->execute($sqlString);
          $sql->setDelimiter();
          if ($sql->errormsg()) {
            $log->error($sqlString);
            $log->error($sql->errormsg());
          }
        }
      }

      $log->info("Worker " . ($i + 1) . " done processing in " . (time() - $startWork) . " seconds");
      exit 0;
    }
  }

  # See if there are others now
  my $numReaped = 0;
  while ($numReaped < $numToFork) {
    sleep 2;
    while (waitpid(-1, WNOHANG) > 0) {
      $numReaped++;
    }
  }

  undef %has_dns;

  $log->info("DNS Processor updated " . scalar(@devices) . " DNS names");
}

sub updateDeviceNames {
  my @devices = $sql->table("
		use netmri;

		drop temporary table if exists recentlyChanged;

		create temporary table recentlyChanged as
		select distinct dp.DeviceID
		from   netmri.DeviceProperty dp left join netmri.Device d on dp.DeviceID = d.DeviceID
		where  dp.PropertyName = 'sysName' and d.Type not like 'SDN%'
		and    dp.Timestamp >= from_unixtime($lastRun);

		select dp.DeviceID, dp.Source, dp.Value, unix_timestamp(dp.Timestamp) as Timestamp
		from   netmri.DeviceProperty dp,
		       recentlyChanged r
		where  dp.DeviceID = r.DeviceID
		and    dp.PropertyName = 'sysName';

		-- This table is dropped later on.
		-- drop temporary table if exists recentlyChanged;
	", AllowNoRows => 1);

  my %deviceNames = ();

  my $now = time();
  foreach my $device (@devices) {
    ## If the collected property is within the past 3 days then consider it as
    ## a possibility.  Otherwise a an old sysName could be the name of a device
    ## that isn't that device anymore or an old DNS name could be the name of a
    ## device that it isn't anymore (DE3034).
    if ($$device{Source} eq "User" || ($$device{Timestamp} + 259200 >= $now)) {
      $log->debug("DevID [" . $$device{DeviceID} . ']->{' . $$device{Source} . '}: ' . $$device{Value}) if $debug;
      $deviceNames{$$device{DeviceID}}->{$$device{Source}} = $$device{Value};
    }
  }

  my ($name);

  foreach my $device (keys %deviceNames) {
    if ($$config{NamePriority} eq 'SNMP') {
      $name =
           $deviceNames{$device}->{User}
        || $deviceNames{$device}->{SNMP}
        || $deviceNames{$device}->{DNS}
        || $deviceNames{$device}->{FingerPrint}
        || $deviceNames{$device}->{'Call Server'}
        || "unknown";
    } else {
      $name =
           $deviceNames{$device}->{User}
        || $deviceNames{$device}->{DNS}
        || $deviceNames{$device}->{FingerPrint}
        || $deviceNames{$device}->{SNMP}
        || $deviceNames{$device}->{'Call Server'}
        || "unknown";
    }

    for my $strip_domain (@searchDomains) {
        $strip_domain = ".$strip_domain" if substr($strip_domain, 0, 1) ne ".";
        $log->debug("Check domain: '$strip_domain'");
        $name =~ s/\Q$strip_domain\E$//i;
    }

    # Remove anything that is not a letter,
    # digit, period, dash, colon or underscore
    $name =~ s/([^\w\-\.\#\:])//g;

    $log->debug("Name update: Name [$name] " . "Dev [$device]") if $debug;
    $sql->execute("update netmri.Device set Name = '$name' " . "where DeviceID = $device");
    if ($sql->errormsg()) {
      $log->error("Error Updating $device: " . $sql->errormsg());
    }
  }

  $log->info("DNS Processor updated " . scalar(keys %deviceNames) . " device names.");
}

sub raiseIssues {
  my @ipAddrTable = $sql->table("
		select DeviceID, IPAddressDotted
		from   netmri.ifAddr
	", AllowNoRows => 1);

  my %deviceAddrs = ();

  for (my $i = 0; $i < @ipAddrTable; $i++) {
    $deviceAddrs{$ipAddrTable[$i]->{DeviceID}}->{$ipAddrTable[$i]->{IPAddressDotted}} = 1;
  }

  my @devices = $sql->table("
		drop temporary table if exists sysNameList;

		create temporary table sysNameList as
		select dp.DeviceID, dp.Value as sysName
		from   netmri.DeviceProperty dp,
		       recentlyChanged r
		where  dp.DeviceID = r.DeviceID
		and    dp.PropertyName = 'sysName'
		and    dp.Source = 'SNMP';

		drop temporary table if exists deviceList;

		create temporary table deviceList as
		select DeviceID,
		       Name,
		       IPAddress,
		       Type,
		       TypeProbability,
		       1 as SNMPAnalysis
		from   netmri.Device
		where  SNMPReadSecure is not null;

		alter table deviceList add index(DeviceID);

		update deviceList d, DeviceProperty dp
		set    d.Name = Value
		where  '$$config{NamePriority}' != 'DNS'
		and    d.DeviceID = dp.DeviceID
		and    dp.PropertyName = 'sysName'
		and    dp.Source = 'DNS';

		update deviceList d, DeviceGroupSettings s
		set    d.SNMPAnalysis = s.SNMPAnalysis
		where  d.DeviceID = s.DeviceID;

		alter table deviceList add DeviceLicensed int default 0;

                update deviceList d, report.DeviceConfig c
                set    d.DeviceLicensed = c.DeviceLicensed
                where  d.DeviceID = c.DeviceID;

		select d.*, sysName
		from   deviceList d,
		       sysNameList s
		where  d.DeviceID = s.DeviceID
		and    d.Type not in ('HSRP','VRRP','GLBP')
		and    d.DeviceLicensed = 1;

		drop temporary table if exists recentlyChanged;
		drop temporary table if exists sysNameList;
	", AllowNoRows => 1);

  $log->error($sql->errormsg()) if ($sql->errormsg());

  my $dnsRes = Net::DNS::Resolver->new;
  $dnsRes->persistent_udp(1);
  $dnsRes->udp_timeout(2);
  $dnsRes->retry(1);
  $dnsRes->retrans(1);
  $dnsRes->domain(@searchDomains);
  $dnsRes->debug($debug);

  my $issueClient = NetMRI::Analysis::IssueClient->new(undef, $log);

  my $debugAnalysis = (-e "/tmp/analysis.debug");

  my $numRaised = 0;

  foreach my $device (@devices) {

    ## Add in the management IP address to handle those bad devices
    ## that don't have it in the ipAddrTable for some reason.
    $deviceAddrs{$$device{DeviceID}}->{$$device{IPAddress}} = 1;
    my $ip = $$device{IPAddress};

    ## Lookup sysname using search list.
    ## If result doesn't map into %deviceAddrs then raise an issue.
    my $dnsMatch = !$$device{SNMPAnalysis};
    if (!$dnsMatch) {
      my $sysName = $$device{sysName};
      my $qType = ($ip =~ /:/ ? 'AAAA' : 'A');

      # Trying with each DNS server until we get a response or run out of servers
      # If we put dns servers array in the nameserver method, it will return undef
      # in case of NXDOMAIN, but another server may have the answer.
      $log->debug("DNS Servers: " . join(", ", @dnsServers)) if $debug;
      my $query;
      foreach (@dnsServers) {
        $log->debug("Query: Name ['$sysName'] IP ['$ip'] Type ['$qType'] DNS ['$_']") if $debug;
        $dnsRes->nameserver($_);
        # Make an A or AAAA query depending on address type
        $log->debug("dnsRes: " . $dnsRes->string) if $debug;
        $query = $dnsRes->search($sysName, $qType);
        last if $query;
        $log->debug("Failed with NS response. " . $dnsRes->errorstring) if $debug;
      }
      if ($query) {
        $log->debug("Got NS response. " . "Checking...") if $debug;
        foreach my $rr ($query->answer) {
          $log->debug("Found " . $rr->type . ' record.') if $debug;
          next unless ($rr->type eq $qType);

          # If AAAA record, compress addr
          # before checking for matches.
          my $addr = $rr->address;
          if ($qType eq 'AAAA') {
            $addr = Net::IPv6Addr::to_string_compressed($rr->address);
          }

          if ($debug || $debugAnalysis) {
            $log->debug("REAL-TIME: Checking $sysName $ip");
            $log->debug("\t+++ " . $addr);
          }
          $dnsMatch = $deviceAddrs{$$device{DeviceID}}->{$addr};
          last if $dnsMatch;
        }
      } else {
        $log->debug("query is null: " . $dnsRes->errorstring) if $debug;
      }
    }

    $log->debug("dnsMatch: [" . ($dnsMatch ? 'True' : 'False') . ']') if $debug;
    if (!$dnsMatch) {
      $numRaised += 1;
      $log->debug("Raising sysNameDNSError for device: " . $$device{DeviceID}) if $debug;
      $issueClient->issueSeen(
        "DeviceSysNameDNSError",
        { DetailID => $$device{DeviceID},
          DeviceID => $$device{DeviceID}});
    }
  }

  $log->info("DNS Processor raised $numRaised issues");
}
