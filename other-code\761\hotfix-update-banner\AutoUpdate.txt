#!/usr/bin/perl --

use strict;
use Term::ReadKey;

use Proc::Daemon;
use File::Path qw(rmtree);
use POSIX ":sys_wait_h";
use POSIX "setsid";
use Fcntl;

use Getopt::Long;
use Net::Ping ();
use Date::Parse ();
use Cwd qw(abs_path getcwd);
use IPC::Open3 qw(open3);
use MIME::Base64;

use NetMRI::API;
use NetMRI::SecureChannel;
use NetMRI::Util::Process qw(handle_signals);
use NetMRI::Util::Format;
use NetMRI::Event::EventClient;
use NetMRI::Util::AutomationControl;
use NetMRI::Util::Drbd;
use NetMRI::Util::HW;
use NetMRI::Config;
use NetMRI::Util::Input qw(askYesNoQuestion);

our $skipjack = "/tools/skipjack";
require "$skipjack/bin/SQL-COMMON.pm";
require "$skipjack/bin/COMMON-CFG.pm";
require "$skipjack/bin/COMMON-UP.pm";
require "$skipjack/bin/COMMON-HW.pm";

# Some tricks around big global namespace
# which used in XXXX.pm files.
# reassign &abort entry point to local
# version of this function
*main::abort = *main::_abort;

our $productName = NetMRI::Util::AutomationControl::brandLookup("PRODUCT_NAME");

## Check to see if someone did "su admin" instead of "su - admin"
## or logging in directly as root.

if (getcwd() ne "/var/home/<USER>/chroot-home/Backup") {
	print "Cannot login to admin using \"su\". Instead login directly or use \"su - admin\".\n";
	exit 1;
}

my $daemon_pid_file = "/var/run/netmri.autoupdate.daemon.pid";
my $auPid = checkUpdateIsRunning();

my $doCleanTmp = 1;
cleanTmp();

my $daemon_exit_file = "/dev/shm/netmri.autoupdate.daemon.exit.success";
unlink $daemon_exit_file;

# Clean up reboot file from any previous AutoUpdate
my $rebootFile = '/dev/shm/upgrade-needs-reboot';
unlink $rebootFile;

#
# Autoflush STDOUT
#
$|=1;

our $Version     = "";
our $SerialNo    = "";
our $NetworkName = "";
our $Model      = "";
our $NetMRIMode = "";
our $AppDir     = "";
our $MaintenanceExpiration = "";
our $mysqlHost   = "";
our $mysqlPass   = "root";
our $httpPort   = "";
our $httpsPort  = "";
our $AutoCopyDest1 = "";
our $AutoCopyDest2 = "";
our $skipBackup = 0;
our $AutoUpdateSettings = "Disabled";
my %upgradeDetails;
my $hw = getHWInfo();
my $macAddress  = "";
my $hostname    = "techdata.infoblox.com";
my $username    = "netmri";
my $versionFile = "";
my $skipjackIsRunning = 1;
my $skipjack_stopped_by_autoupdate = 0;
my $daemonized = 0;
my $enableENDBlock = 1;
my $test_mode = 0;
my $test_dir = '/home/<USER>/Backup/AUtest/';

# If Maintenance is Expired, then MaintenanceExpired will be 1
#    - this will not allow the updates to be applied
my $MaintenanceExpired = 1;
my $MaintenanceMsg = "";

loadCfgFile("$skipjack/conf/server.cfg");

if($Model eq "Now") {
	$hostname = "techdata2.infoblox.com";
}

my $doAutomatic = 0;
my $doSilent = 0;
# Force update in background
my $forceBackground = 0;
my $forceMajor = 0;
my $isCli = 0;
my $excludeMajor = 0;
my $excludeMinor = 0;
my $doFailoverUpdate = 1;
my $noDaemonize = 0;

my $autoUpdatesAvail = 0;
# Keys - "HotFix", "Major";
# values -
#  undef (unavailable) /
#  0 (all updates successfully installed) /
#  >0 (updates left or still not installed)
my %typeOfUpdatesAvail = ();
my %updateTypeNames = (
	HotFix => "HotFix" ,
	Major => "Major Update"
);

my $autoResult = "";

my $hr="-------------------------------------------------------------------------";

my $AutoUpdateMsg = <<EOF;
++++++++++++++++++++++++++++++++++++++++++++++++++++++++
  The AutoUpdate script has been updated and will now
  be automatically re-executed to continue the update
  process.
++++++++++++++++++++++++++++++++++++++++++++++++++++++++
EOF

my @checksums      = ();
my @filesizes      = ();
my @neededFiles    = ();
my @neededVersions = ();
my @timestamps     = ();
my $majorAvail = 0;
my $minorAvail = 0;

# Remove any obsolete log file from version 1.0.1
doCommand("/bin/rm -f /home/<USER>/AutoUpdate.log");

my $user = $ENV{'USER'} || $ENV{'LOGNAME'};

print "AU settings : '$AutoUpdateSettings'\n";
if ($AutoUpdateSettings =~ m/^Test,/) {
	$test_mode = 1;
}

my $logFile = "/home/<USER>/Backup/AutoUpdate.log";
my $upgradeLog = "/home/<USER>/Backup/UpdateHistory.log";
my $reboot_reason_file = "/tmp/reboot_reason";
$upgradeDetails{'reboot'} = 0;
#
#       Rotate the log files
#
my $logFileMax = 5;
my $i;
for ($i=$logFileMax-1; $i>0; $i--) {
    my $next = $i + 1;
    if (-f "$logFile.$i") {
        rename("$logFile.$i", "$logFile.$next");
    }
}
if (-f $logFile) { rename("$logFile", "$logFile.1"); }

# Validate input before processing options for security.
foreach my $arg (@ARGV) {
	abort("Invalid Input") if $arg =~ /[^\w\.\-\=]/;
}

Getopt::Long::Configure("bundling_override");
Getopt::Long::GetOptions(
    'cli' => sub {
        $isCli = 1;
    },
    'auto' => sub {
        $doAutomatic = 1;
        $doSilent = 1;
    },
    'force-major' => sub {
       	$forceMajor = 1;
    },
	'silent' => sub {
		$doSilent = 1;
    },
	'bg' => sub {
		$forceBackground = 1;
	    $doSilent = 1;
	},
	'minor' => sub {
		$excludeMajor = 1;
	},
	'skipfailover' => sub {
		$doFailoverUpdate = 0;
        $noDaemonize = 1;
	},
	'skipbackup' => sub {
		$skipBackup = 1;
	},
	'test' => sub {
		$test_mode = 1;
	}
);

# Check command line args
my $optionsAvailable = scalar @ARGV;

if($NetMRIMode eq "slave" && ((!$optionsAvailable ) || $doAutomatic)) {
	print "+++ This operation is not supported on collectors";
	exit(1);
}

#
#       Open a new log file
#
open(LOG, ">$logFile");

handle_signals('IGNORE', qw{INT QUIT});

logMsg( "********** AutoUpdate Started **********" );
logMsg( "********** TEST MODE **********" ) if $test_mode;

#
#       Create a temporary directory that is only accessible by root
#       so that the user can't see the decrypted package files
#
my $bgDir = "/home/<USER>/chroot-home/tmp/$auPid";
my $tmpDir = '';
if ( $doAutomatic ) {
        $tmpDir = $bgDir;
}
else {
    $tmpDir = "../tmp/$auPid";

	# We want an anchored path to pass
	# through to background processes.
	$bgDir = abs_path( $tmpDir );
}

sqlOpen("root", $mysqlPass);

my $drbd = NetMRI::Util::Drbd->new();
my $fo_cfg = $drbd->getFailOverConf();
# define if we are failover secondary
my $secondaryRole = 1 if $fo_cfg && $fo_cfg->{local_index} && $fo_cfg->{local_index} != $fo_cfg->{primary_index};

#
# Find out whether or not the Maintenance Agreement has expired.
# checkMaintenanceExpiration only checks to see if the maintenance is valid.
checkMaintenanceExpiration ();

if (-e $tmpDir) {
    doCommand("/bin/rm -fr $tmpDir");
}

doCommand("/bin/mkdir -p $tmpDir");
doCommand("/bin/chmod 700 $tmpDir");

#
#       Store a copy of the AutoUpdate script in the temporary
#       directory so that we can determine if this script has
#       been updated during the update process.  If so, we'll
#       automatically invoke the new script to keep going.
#
doCommand("/bin/cp -f /home/<USER>/AutoUpdate $tmpDir");

#
#       Create an SSH config file to be used by our commands
#       instead of the default config file in /root/.ssh
#
my $sshCfg = "$tmpDir/sshConfig";
my $scpCommand = "/usr/bin/scp -F $sshCfg -Bq $username\@$hostname:update";
my $sshCommand  = "/usr/bin/ssh -F $sshCfg -x $username\@$hostname";

open (CONFIG, ">$sshCfg");
print CONFIG <<EOF;
Host *
StrictHostKeyChecking no
UserKnownHostsFile /dev/null
UserKnownHostsFile2 /dev/null
EOF
close (CONFIG);

#
#       If a release file was specified then process it without
#       connecting to the website
#

# need to track success to exit properly
my $success = 1;
my $installed_updates = 0;

# save failover enable state before updates
my $failover_enabled = NetMRI::Util::Drbd::isFailoverEnabled();

# flag indicates that failover was disabled during updates and 
# it needs to be enabled after updates if reboot was not scheduled
my $failover_needs_enable = 0;


if ( $optionsAvailable && !$doAutomatic ) {
    # Call performMaintenanceAction to abort the script if maintenance is expired,
    # or continue if maintenance is valid.
    performMaintenanceAction ();

    if ($ARGV[0] eq "cdrom") {
	print "+++ cdrom is no longer supported\n";
	exit 1;
    } else {
        print "\n";
        for my $file ( @ARGV ) {
		my $upgrade_failed = processUpgradeFile (".", $file);

		# Clear upgradeDetails for the next upgrade file
		undef %upgradeDetails;

		if($upgrade_failed) {
			$success = 0;
			last;
		}

		# protection from prompts
		last if $daemonized; 
        }
    }

} else {
	if ( $doAutomatic ) {
                getCurrentVersion();
                if ( ( ! $isCli ) &&
                  ( $AutoUpdateSettings eq "Disabled" ) ) {
                        abort("Automatic Update Disabled");
                }
        }
        
        if ( NetMRI::Util::AutomationControl::brandLookup( "ENABLE_AUTOUPDATE" ) ne 'true' ) {
        	abort( "Automatic Update Disabled" );
        }

        unless ($test_mode) {
		pingHost();
	} else {
		print "#### TEST MODE: skipping pingHost ####\n";
	}
        getCurrentVersion();

	if (!$doAutomatic && !verifyOverwrite()) {
	    exit(0);
	}

    downloadVersionInfo();
    compileVersionList();

	while (my $displayResults = displayVersionList()) {
		# Reload the compiled list
		if($displayResults == 2) {
			compileVersionList();
			next;
		}

		my $status = updateNetMRI();
		$installed_updates++;

		last unless $status;
		compileVersionList();

		if ( $doAutomatic ) {
			last unless ( $forceMajor || $typeOfUpdatesAvail{ HotFix } );
		}

		# Clear upgradeDetails for the next upgrade file
		undef %upgradeDetails;

		# protection from prompts
		last if $daemonized; 
    }
}

# restore failover status if reboot was not scheduled

if ($failover_needs_enable) {
    if  (-e $reboot_reason_file) { 
		logMsg("+++ Reboot scheduled, leaving failover disabled...");
    } else {
		logMsg("+++ Enabling the failover after system update...");
		doCommand("/home/<USER>/adminShell -c failover enable -s -r");
    }
}

# Make sure any notifier cfg is gone
if ($NetMRIMode ne "slave" && $installed_updates) {
	cleanNotifier();
	# Reload httpd settings if skipjack has already been restarted
	if($skipjackIsRunning) {
		system("/bin/systemctl restart httpd.service >/dev/null 2>&1");
		# Stop and restart if the restart failed
		stopSkipjack() if $?;
	}
}

doCommand("/bin/rm -fr $tmpDir");

##
## Do any issue raising or clearing.  This is even done in manual
## mode.
##

my %device = sqlRecordHash("
        set \@ipAddr := '';

        select \@ipAddr := Value
        from   netmri.DeviceProperty
        where  DeviceID = 0
        and    PropertyName = 'pIPAddress';

        select *
        from   netmri.Device
        where  Type = 'NetMRI'
        and    IPAddress = \@ipAddr;
");

my $AutoUpdateAvail = "false";
my $AutoUpdateDescription = "";
if ( $doAutomatic ) {
    if ( $autoUpdatesAvail ) {

        my $eventClient = new NetMRI::Event::EventClient();

        # Save off AutoUpdate.log
        mkdir "$AppDir/autoupdate" if ( !-d "$AppDir/autoupdate" );
        my $timestamp = substr(genTimestamp(),0,10);
        doCommand("/bin/cp /home/<USER>/Backup/AutoUpdate.log $AppDir/autoupdate/AutoUpdate.log.$timestamp");

        sleep 10;       # Give it a chance to startup.

        my @descrStack;
        while ( my ( $t , $n ) = each %typeOfUpdatesAvail ) {
            raiseUpdateEvent(
                $eventClient ,
                $updateTypeNames{ $t } ,
                $n
            );

            push( @descrStack , "$t:$n" ) if ( $n > 0 );
        }

        if ( @descrStack ) {
            $AutoUpdateAvail = "true";
            $AutoUpdateDescription = join( "," , @descrStack );
        }

        sqlExecute("
            insert into netmri.Settings (Name,Value)
            values
            ( 'AutoUpdate.action', '$device{auAction}' ),
            ( 'AutoUpdate.update', '$autoResult' ),
            ( 'AutoUpdate.timestamp', now() )
        ");
    }
}

# Set/clear flag if we should show "Upgrades Available" in GUI
writeToCfg("$skipjack/conf/server.cfg", "AutoUpdateAvail", $AutoUpdateAvail);
writeToCfg("$skipjack/conf/server.cfg", "AutoUpdateDescription", $AutoUpdateDescription);

if ($success == 1) {
	logMsg("********** AutoUpdate Completed **********");
} else {
	logMsg("********** AutoUpdate Completed With Errors **********");
}

my $return_code = $success == 1 ? 0 : 1;

finalize();

close ( LOG );
exit($return_code);

sub raiseUpdateEvent {
	my ( $eventClient , $updateTypeName , $updatesNum ) = @_;

	if ( $updatesNum == 0 ) {
        $eventClient->add("SystemAlert","",'NetMRI Update',
		  '','',"NetMRI $updateTypeName Installed");
        } else {  
        	$eventClient->add("SystemAlert","",'NetMRI Update',
		  '','',"NetMRI $updatesNum $updateTypeName(s) Available");
        }
}

######################################################################
#
#  Attempt to ping the desired hostname.  If it fails the update
#  attempt is aborted.
#
sub pingHost
{
    my $result = `/bin/ping -w5 -n -c1 $hostname 2>/dev/null`;
    logMsg($result);
    my $status = $?;

    if ( $status != 0 && !$doAutomatic ) {
        print "*** Unable to ping '$hostname'. Continuing to attempt update. ***\n";
    }
}

######################################################################
#
#       Get the version information from the config file
#
sub getCurrentVersion
{
    loadCfgFile("$skipjack/conf/server.cfg");

    if ( $Version eq "" || $Version eq "INVALID" ) {
        abort("ERROR: Cannot determine current NetMRI version");
    }

    my $ifCfg = `/sbin/ifconfig eth0`;
    ( $macAddress ) = ( $ifCfg =~ /HWaddr ([A-Z0-9:]+)/ );

    logMsg("Current NetMRI Version: $Version SerialNo: $SerialNo Network: $NetworkName");
}

sub verifyOverwrite
{

	my $doScriptPatched = "y";
	# check if the system has been patched since the last version install
    if (!$doSilent && -e $upgradeLog) {
		open (UPGRADELOG,"<$upgradeLog") || abort("Upable to read upgrade history");
		my @changes = <UPGRADELOG>;
		close(UPGRADELOG);

		if ($#changes > 0) {
			my $foundcurrent = 0;
			my @current_updates;

			foreach my $change (@changes) {
				chomp($change);
				if ($foundcurrent && length($change) > 1) { 
					# parse out the update name
					$change =~ /(.+?\s+.+?)\s+(.+?)\s\s+.*/;
					my $time = $1; my $patch = $2;
					# don't care if update is same version or License applied
					unless ($patch =~ /netmri-\d+\.\d+\.\d+/ ||
						$patch =~ /NetworkAutomation-MR-v\d+\.\d+\.\d+/ ||
						$patch =~ /ib_network_automation-\d+\.\d+\.\d+/ ||
						$patch eq 'License Update') {
						if (($change !~ /\*\*\*/) &&
						    (length($patch) > 1) &&
						    ($patch !~ /EnableRootDiag/)) {
							push (@current_updates,"$time - $patch");
						}
					}
				} elsif (($change =~ /netmri-$Version/) ||
                                         ($change =~ /NetworkAutomation-MR-v$Version/) ||
                                         ($change =~ /ib_network_automation-$Version/)) {
					$foundcurrent = 1;
				}
			}

			if ($foundcurrent == 0) {
				# everything is a change
				foreach my $change (@changes) {
					chomp($change);
					# parse out the update name
					$change =~ /(.+?\s+.+?)\s+(.+?)\s\s+.*/;
					my $time = $1; my $patch = $2;
					unless ($patch =~ /netmri-\d+\.\d+\.\d+/ ||
						$patch =~ /NetworkAutomation-MR-v\d+\.\d+\.\d+/ ||
						$patch =~ /ib_network_automation-\d+\.\d+\.\d+/ ||
						$patch eq 'License Update') {
						if (($change !~ /\*\*\*/) &&
						    (length($patch) > 1) &&
						    ($patch !~ /EnableRootDiag/)) {
							push (@current_updates,"$time - $patch");
						}
					}
				}
			}


			if (@current_updates > 0) {
				# updates have been applied
                print "\n$hr\n";
		
                print "This system has been patched since the last update was applied. It is \n";
				print "possible that upgrading will overwrite these patches. If you are unsure\n";
				print "about what has been applied, please contact $productName support.\n\n";
				print "The following patches have been applied since your last upgrade:\n\n";
				logMsg("+++The following patches have been applied since your last upgrade.");

				foreach (@current_updates) {
					logMsg("Existing Patch: $_");
					print "\t$_\n";
				}
                print "$hr\n\n";
                $doScriptPatched = askYesNoQuestion("Do you want to continue, possibly overwriting these patches?", "y", 1);
                print "\n";
			}
		}
    }
    if ($doScriptPatched ne "y") {
        logMsg("+++ Update skipped by user due to existing patches.");
		return(0);
	}
	return(1);
}

sub getAdditionalInfo {

	my $config = NetMRI::Config->new(ContinueIfMissing => 1, SkipAdvanced => 1);

	my $cpus;
	if ($^O eq "linux") {
		open my $h, "<", "/proc/cpuinfo";
		my @info = <$h>;
		close $h;
		$cpus = scalar(map /^processor/, @info);
	}

	my %fields = sqlRecordHash("call report.DiscoveryStatistics()");

	my %info = (
		cpus                        => $cpus,
		licensedCount               => $fields{licensedCount},
		EffectiveDeviceLicenseLimit => NetMRI::Util::Format::numberFormat($config->{EffectiveDeviceLicenseLimit}),
		deviceCount                 => $fields{deviceCount},
		interfaceCount              => $fields{interfaceCount},
	);

	$info{$_} ||= 'N/A' for keys %info;

	return "--cpu_count $info{cpus} --licensed_count $info{licensedCount} --effective_device_license_limit $info{EffectiveDeviceLicenseLimit} --devices_count $info{deviceCount} --interfaces_count $info{interfaceCount}";
}

######################################################################
#
#  Use scp to download the NetMRI version information file.  If
#  an error occurs parse the output and be as specific as possible
#  to the user.  Log any error output from scp to the log file.
#
#  NETMRI-17887: Include version for getVersions request. This will
#  add more control via techdata.
#
sub downloadVersionInfo
{
    print "+++ Downloading List of $productName Updates from $hostname ... \n";

    my $additionalInfo = getAdditionalInfo();

    unless ( $test_mode ) {
        $versionFile = `$sshCommand '/home/<USER>/bin/getVersions $SerialNo $macAddress $Version $$hw{Model} $$hw{Memory} $additionalInfo'`;
    } else {
        print "#### TEST MODE: downloadVersionInfo override ####\n";
        $versionFile = `/bin/cat $test_dir/versionFile.txt`;
    }

    if ( $versionFile eq "" || $versionFile !~ /Version Information/ ) {
        if ( $versionFile =~ /Host key verification failed/ ) {
            abort("No host key installed for '$hostname'");
        }
        elsif ( $versionFile =~ /No address associated with hostname/ ) {
            abort("Invalid hostname '$hostname'");
        }
        elsif ( $versionFile =~ /Permission denied \(publickey/ ) {
            abort("Permission denied for '$username\@$hostname'");
        }
        elsif ( $versionFile =~ /scp:.*Permission denied/ ) {
            abort("Access denied to file '$versionFile' on '$hostname'");
        }
        elsif ( $versionFile =~ /No such file or directory/ ) {
            abort("Invalid directory '$versionFile' on '$hostname'");
        }
        else {
            $versionFile =~ s/\n/ /g;
            $versionFile =~ s/\r//g;
            abort("Transmission Error: $versionFile");
        }
    }
}
# Update techdata.infoblox.com with a cookie indicating
# the current version of NetMRI that is running.  It is
# a silent operation.
#
# NETMRI-17887:
#               SerialNo (Serial number)
#               Version (current version of product)
#       Include additional information:
#               upgradeName (Name of the patch)
#               checksum (Checksum of the patch)
#               status (Execution status from the upgrade script)
#               duration (Timespan to complete the patch run)
#		NetMRIMode (standalone,master,slave)
#		hwModel (HW platform / server type)
#		hwMemory (Installed Memory)
#
sub sendUpdateResults
{
	my @results = ();

	$upgradeDetails{'NetMRIMode'} = $NetMRIMode;
	$upgradeDetails{'hwModel'} = $$hw{Model};
	$upgradeDetails{'hwMemory'} = $$hw{Memory};
	$upgradeDetails{'duration'} = $upgradeDetails{'end'} - $upgradeDetails{'start'};

	foreach ('upgradeName','checksum','status','duration','NetMRIMode','hwModel','hwMemory') {
		my $item = $upgradeDetails{$_} ? $upgradeDetails{$_} : 'N/A';
		push(@results, $item);
		
	}

	push(@results, getAdditionalInfo());

	&doCommand("$sshCommand '/usr/bin/sudo /home/<USER>/bin/addAutoUpdateLog $SerialNo $Version @results'; true");
}


# Reboot or just start NetMRI after AutoUpdate is finished.
sub finalize
{
	logMsg("+++ Checking if a reboot is needed...");
   
    my $mark = `/bin/cat $rebootFile 2> /dev/null`;
    chomp $mark;
    $mark = 0 unless $mark gt 0; #immediate if not otherwise

    # Return unless upgrade is finished successfully
	return unless((
		($upgradeDetails{'status'} == 0) &&  exists $upgradeDetails{'end'}) ||
		(!exists $upgradeDetails{'status'} && !exists $upgradeDetails{'end'} && $return_code == 0)
	);
	
	# If we don't have to reboot, just start skipjack
	if ($upgradeDetails{'reboot'} == 0) {
		if ($secondaryRole){
			logMsg("+++ HA Secondary, leaving $productName stopped...");
		} else {
			startSkipjack();
		}
		return;
	}

	system("/usr/bin/sudo /home/<USER>/prepareForReboot");
	createRebootReasonFile("update");
	$failover_needs_enable = 0;

	logMsg("+++ NetMRI is being rebooted...");
	exec("/tools/skipjack/bin/netmri_reboot.pl '--reason=Reboot from Autoupdate'");
}

######################################################################
#
#  Break the given version number into numeric parts
#
sub getVersionNumbers
{
        my ($version) = @_;

        #if ($version !~ /^(\d+)\.(\d+)([bp\.])?(\d+)?([a-z])?$/) {
        if ($version !~ /^(\d+)\.(\d+)([bp\.])?(\d+)?([a-z\.])?(\d+)?$/) {
            abort("Invalid Version Format '$version'");
        }

        ## Prior to 2.3.1
        ## 1.5          cMajor=1 cMinor=5 rev= cRev=0 pre=
        ## 1.5p4        cMajor=1 cMinor=5 rev=p cRev=4 pre=
        ## 1.5p4a       cMajor=1 cMinor=5 rev=p cRev=4 pre=a
        ## 1.6b1        cMajor=1 cMinor=6 rev=b cRev=1 pre=
        ##
        ## 1.5 < 1.5p4 < 1.5p4a < 1.6b1a < 1.6b1 < 1.6
        ##
        ## After 2.3.1
        ##
        ## 2.3 < ******* < ******* < 2.3.1 < ******* < 2.3.2
        ##

        my $cMajor = $1;
        my $cMinor = $2;
        my $patch    = $3 || "";
        my $cPatch   = $4 || 0;
        my $build    = $5 || "";
        my $cBuild   = $6 || 0;

        if ($patch eq "b") {
                $cPatch = -2500 + 50 * $cPatch;
                if ($build ne "") {
                        $cPatch -= ord('z') - ord($build) + 1;
                }
        }
        elsif ( $cBuild == 0 ) {                ## Prior to 2.3.1
                $cPatch *= 50;
                if ($build ne "") {
                        $cPatch += ord($build) - ord('a') + 1;
                }
        }

        return ($cMajor, $cMinor, $cPatch, $cBuild);
}

######################################################################
#
#  Read the NetMRI version information file downloaded and
#  determine what versions need to be downloaded and applied.
#
sub compileVersionList
{
    print "+++ Compiling List Of Required Upgrades ... \n";

    @checksums      = ();
    @filesizes      = ();
    @neededFiles    = ();
    @neededVersions = ();
    @timestamps     = ();

    my ( $cMajor, $cMinor, $cPatch, $cBuild ) = getVersionNumbers($Version);

    my ( @lines ) = split (/\n/, $versionFile);

    ##
    ## Micro patches are v1.5p4-Name.gpg.  This gets applied to any 1.5p4 release
    ##
    my $pMajor = 0;
    my $pMinor = 0;
    my $pPatch = 0;
    my $pBuild = 0;
    my $isMicroPatch = 0;
    my $isMajorUpdate = 0;
    my $isMRUpdate = 0;
    %typeOfUpdatesAvail = ();
    $majorAvail = 0;
    $minorAvail = 0;

    for my $line ( @lines ) {

        chomp ( $line );

        logMsg("\t$line");

        next if ( $line =~ /^#/ || $line eq "" );

        my ($version, $filename, $checksum, $filesize, @dateParts) = split(/\s+/, $line);

        my $creationDate = join(" ", @dateParts);

        ##
        ## Micro patches are v1.5p4-Name.gpg.  This gets applied to any 1.5p4 release
        ##
        $pMajor = 0;
        $pMinor = 0;
        $pPatch = 0;
        $pBuild = 0;
        $isMicroPatch = 0;
        $isMajorUpdate = 0;

	## Look for Maintenance Releases.
	## Clear needed hoxfixes matching the current version if a newer MR version is available.
	##
	if ( $filename =~ /NetworkAutomation-MR-v(\d+)\.(\d+)\.(\d+)\.\d+/ ) {
		my @Ver = split(/\./,$Version);
		($pMajor, $pMinor, $pPatch) = ( $1, $2, $3 );
		if( $Ver[0] eq $pMajor && $Ver[1] eq $pMinor && $Ver[2] < $pPatch ) {
			$isMRUpdate = 1;
			$isMicroPatch = 1;
			foreach(@neededFiles) {
				if ( $_ =~ /NetworkAutomation-MR-v\d+\.\d+\.(\d+)\.\d+/) {
					if ( $1 > $pPatch) {
						$isMicroPatch = 0;
					}
				}
			}
		}
	}
	elsif ( $filename =~ /v\d+\.\d+/ ) {
		## Stop adding hotfixes if a Maintenance Release upgrade was found earlier.
		next if($isMRUpdate);

        my $pVer = $filename;
		my $sVer = $Version;
        $pVer =~ s/^v//;
        $pVer =~ s/\-.*//;
		$sVer =~ s/^(\d+\.\d+\.\d+).*/$1/;
        if ( $sVer eq $pVer ) {
#1.5p4a   netmri-1.5p4a.gpg        64898   1115992  2006-11-07 10:13:26
                        my $patchName = $filename;
                        $patchName =~ s/\.gpg//;
                        my $prev = `/bin/grep $patchName $upgradeLog 2> /dev/null | /usr/bin/tail -1`;
                        chomp $prev;
                        if ( $prev eq "" ) {
                                $isMicroPatch = 1;
				$typeOfUpdatesAvail{ HotFix } += 1;
                        }
                        else {
                                my ($sum) = ($prev =~ /checksum = (\d+)/);
                                if ( $checksum ne $sum ) {
                                        $isMicroPatch = 1;
					$typeOfUpdatesAvail{ HotFix } += 1;
                                }
                        }
                }
        }
        else {
                ( $pMajor, $pMinor, $pPatch, $pBuild ) = getVersionNumbers($version);

                $isMajorUpdate = ( $pMajor > $cMajor ) ||
                  ( $pMajor == $cMajor && $pMinor > $cMinor ) ||
                  ( $pMajor == $cMajor && $pMinor == $cMinor && $pPatch > $cPatch ) ||
                  ( $pMajor == $cMajor && $pMinor == $cMinor && $pPatch == $cPatch &&
                   (($pBuild == 0 && $cBuild > 0) || $pBuild > $cBuild));

		$typeOfUpdatesAvail{ Major } += 1 if ( $isMajorUpdate );
        }
        if (($isMicroPatch && !$excludeMinor) || ($isMajorUpdate && !$excludeMajor)) {

            $majorAvail += 1 if($isMajorUpdate);
            $minorAvail += 1 if($isMicroPatch);

            push ( @neededFiles, $filename );
            push ( @neededVersions, $version );
            push ( @checksums, $checksum );
            push ( @filesizes, $filesize );
            push ( @timestamps, $creationDate );
        }
    }
    $autoUpdatesAvail = $doAutomatic && ($typeOfUpdatesAvail{Major} || $typeOfUpdatesAvail{HotFix});
}

######################################################################
#
#  Display the list of NetMRI software versions that need
#  to be applied to the current system.
#
sub displayVersionList
{
    print "\nCurrent Version = $Version\n";

    if ( !@neededFiles ) {
        report("\n*** No Upgrades Are Required For This Version ***\n");
        return 0;
    }

    print "\nThe following updates need to be applied:\n\n";

    for ( my $i=0; $i < @neededVersions; $i++ ) {
        my $nVersion = $neededVersions[$i];
        my $nDate = $timestamps[$i];
        if ( $doAutomatic ) {
                $autoResult .= "," if ( $autoResult ne "" );
                $autoResult .= $nVersion;
        }
        logMsg("Need $nVersion");
        printf "\t%-6s ($nDate)\n", $nVersion;
    }

    performMaintenanceAction ();

    if ( $doAutomatic ) {
        if ( $isCli ) {
            return 1;
        } elsif ( $AutoUpdateSettings =~ /Notify Only/ ) {
            return 0;
        }
        else {
			return 1;
        }
    }
    else {
            if ($majorAvail && $minorAvail) {
                print "\nMajor and hotfix updates are both available.";
                print "\nDo you want to upgrade to the next major release (y/n)? ";
    
                my $ans = <STDIN>;
                chomp ( $ans );

                if ( lc($ans) ne "y" ) {
                    $excludeMajor = 1;
                } else {
                    $excludeMinor = 1;
                }

                return 2;
            }

            print "\nDo you want to upgrade to ";
            if ( @neededFiles == 1 ) {
                print "this version "
            }
            else {
                print "these versions "
            }
            print "now (y/n)? ";

            my $ans = <STDIN>;
            chomp ( $ans );

            print "\n";

            logMsg("Install versions? $ans");

            return 0 if ( lc($ans) ne "y" );

            return 1;
    }
}


######################################################################
#
#  Find out whether or not the maintenance period has expired.
#  If the maintenance period has expired, set the var to 1.
#
sub checkMaintenanceExpiration
{
    #
    # Code added to block the installation of updates if the maintenance period
    # has expired.
    # Simple concept: let the user see what updates are available, then,
    # check to see if the maintenance expiration date in server.cfg is
    # in the future - if so, let the user continue.
    # Else, set the MaintenanceExpired flag and block all updates and
    # tell the user to contact sales to renew their maintenance.
    #
    # If the server.cfg file does not have a MaintenanceExpiration
    # variable set, allow the update to be applied for now. - as of 7/30/2009,
    # this behavior has been changed. The user can upgrade to Atui, but cannot
    # install any autoupdates unless they have updated their license.
    #
    my $companyName = NetMRI::Util::AutomationControl::brandLookup("COMPANY_NAME");
    my $salesPhoneNumber = NetMRI::Util::AutomationControl::brandLookup("PHONE_INFOBLOX_SALES");
    my $salesContactPhrase = "$companyName sales at +$salesPhoneNumber";
    if(length($MaintenanceExpiration)) {
        my $sqlCommand =
            "select datediff(now(), '$MaintenanceExpiration') as Maint";

        my %maintRecord = sqlRecordHash("$sqlCommand");
        if(!exists($maintRecord{ERROR})) {
            # No SQL errors, just continue.
        } else {
            &abort("SQL Datediff for Maintenance Expiration failed.");
        }

        if($maintRecord{'Maint'} > 0) {
            #
            # Maintenance has expired. Updates cannot be applied.
            # Do not abort here though since we want to show the user the
            # updates they are not getting by having expired maintenance.
            #
            $MaintenanceExpired = 1;
            $MaintenanceMsg =
                "Maintenance period expired on $MaintenanceExpiration.\n"
                . "\tUpdates may not be applied. \n"
                . "\tPlease contact $salesContactPhrase to\n"
                . "\trenew your maintenance agreement.";
            logMsg("Maintenance EXPIRED on '$MaintenanceExpiration'. Updates not allowed.");
        } else {
            $MaintenanceExpired = 0;

            if( $NetMRIMode eq 'master') {
                if($secondaryRole){
					logMsg("Skipping the collecors maintenance check: system is in secondary mode.");
                }else {
                    $MaintenanceMsg = checkCollectorsMaintenance( $salesContactPhrase );
                    $MaintenanceExpired = defined $MaintenanceMsg;
                }
            }

            unless( $MaintenanceExpired ) {
                logMsg("Maintenance is currently valid. Expiration date of '$MaintenanceExpiration'.");
                if($maintRecord{'Maint'} > -7) {
                    $MaintenanceMsg .=
                        "WARNING: Your maintenance period is expiring in less than one week.\n"
                        . "\tPlease contact $salesContactPhrase to\n"
                        . "\trenew your maintenance agreement.";
                } else {
                    if($maintRecord{'Maint'} > -30) {
                        $MaintenanceMsg =
                            "WARNING: Your maintenance period is expiring in less than one month.\n"
                            . "\tPlease contact $salesContactPhrase to\n"
                            . "\trenew your maintenance agreement.";
                    }
                }
            }
        }
    } else {
        #
        # This means that the MaintenanceExpiration variable has not
        # been set in the server.cfg file. For now, just allow the update to
        # occur. We could also set a counter to say:
        # you have 3 updates left before you must renew your license
        #
        # jcamp - 20090730 - Pat V and Lou have decided that the user cannot
        # install any updates to NetMRI once they have installed Atui unless they
        # have received a new license file.
        #
        #$MaintenanceExpired = 0;
        #logMsg("Maintenance Expiration check was skipped. Upgrade license.");
        $MaintenanceExpired = 1;
        logMsg("MaintenanceExpiration is not set. License upgrade required to allow updates.");
        $MaintenanceMsg = "ERROR: A license upgrade is required for updates to this $productName release.\n"
            ."\tPlease contact $salesContactPhrase to\n"
            ."\tobtain your license upgrade.";
    }
}

sub getCollectorsRegistry {
    my @collectors = NetMRI::Sql->new(
        DBType  => "mysql",
        DBName  => "netmri",
        DBUser  => "root",
        DBPass  => $mysqlPass,
        DBHost  => $mysqlHost,
        DBPort  => "3306"
    )->table( "select UnitID, UnitIPAddress,
        NetmriDecrypt(UnitPasswordSecure, 'cliCredential',
        SecureVersion) as UnitPassword, UnitName
        from netmri.UnitRegistry where UnitType = 'NetMRI'
        order by UnitID"
    );
    
    return \@collectors;
}

sub checkCollectorsMaintenance {
    my $salesContactPhrase = shift;
    # check for expiration collector's units
    my $rMsg;
    my $units = getCollectorsRegistry();
    checkUnitsForReachability( $units );

    my @reachedUnits;
    my @unreachedUnits = grep {
        if( $_->{ Ping } ) {
            push @reachedUnits, $_;
            0;
        } else {
            1;
        }
    } @$units;

    if( @unreachedUnits ) {
        my $msg;
	$rMsg .= q{
ERROR: The following collectors are not responding
to the license check. Please refer to the
"Settings > Setup > NetMRI Tunnels" table to determine the upgrade
status of these collectors. 
}.        join( "\n", map {
                  $msg = $$_{UnitName}. " (". $$_{ UnitIPAddress }. ")";
                  logMsg("Collector unreachable: $msg");
                  ( $msg );
              } @unreachedUnits
          ). "\n";
    }

    callUnitsLicenseInfoAPI( \@reachedUnits );
    my @unitsWithoutAPI;
    my @unitsWithAPI = grep {
        unless( $$_{ LicenseAPIException } ) {
            1;
        } else {
            push @unitsWithoutAPI, $_;
            0;
        }
    } @reachedUnits;

    if ( @unitsWithoutAPI ) {
        checkUnitsForExpiration(
            callUnitsLicenseInfoCLI(
                \@unitsWithoutAPI,
                'maintenance_expiration'
            ) ,
            'maintenance_expiration'
        );
    }

    if ( @unitsWithAPI ) {
        checkUnitsForExpiration( \@unitsWithAPI, 'maintenance_expiration' );
    }

    my @expired = grep { $$_{ Expired } } @reachedUnits;
    if ( @expired ) {
        my $msg;
        $rMsg .= "Maintenance period of following units expired:\n".
	  join( "\n",
            map {
                $msg = $$_{UnitName}. " (". $$_{ UnitIPAddress }.
                  ") Expired:". $$_{maintenance_expiration};
                logMsg("Collector maintenance expired: $msg");
                ( $msg );
            } @expired
          ). "\n".
          "\tUpdates may not be applied. \n"
                . "\tPlease contact $salesContactPhrase to\n"
                . "\trenew your maintenance agreement.\n";
    }

    my @unknown = grep { $$_{ Unknown } } @reachedUnits;
    if ( @unknown ) {
        my $msg;
        $rMsg .= "Unable to determine maintenance expiration of the following units:\n".
	  join( "\n",
            map {
                $msg = $$_{UnitName}. " (". $$_{ UnitIPAddress }. ")";
                logMsg("Unknown Collector maintenance expiration: $msg");
                ( $msg );
            } @unknown
          ). "\n".
          "\tUpdates may not be applied. \n"
	  . "\tVerify that the admin password on these units is set correctly\n";
    }

    return $rMsg;
}

######################################################################
#
# The script drops out at different points depending on
# the way that AutoUpdate is called. So if the maintenance is
# expired, drop out. If the maintenance expires in 7 days or less,
# print out a warning, but continue.
#
sub performMaintenanceAction
{
    if($MaintenanceExpired) {
        &abort("$MaintenanceMsg");
    } else {
        if(length($MaintenanceMsg)) {
            print "\n\t*** $MaintenanceMsg ***\n";
        }
    }
}

sub isMajorUpdate {
	return ( $_[ 0 ] !~ /^v\d|NetworkAutomation-MR/ );
}

sub reportAsMajor {
	my ($file, $checksum) = @_;
	my $timestamp = genTimestamp();

    appendUpgradeLog("$timestamp  $file  checksum = $checksum status = 1 (skipped as major update)\n");
}

sub downloadFile {
	my ( $file , $targetDir , $expFSize ) = @_;
	
	logMsg("Downloading $file from $hostname");

        unlink "$targetDir/$file" ||
            abort( "Cannot delete file $targetDir/$file" );

        if ($test_mode) {
            print "#### TEST MODE: downloadFile override ####\n";
            doCommand("/bin/cp $test_dir/$file $targetDir/");
        } else {
            # building container for scp
            open( SCPOUT , ">" , "/dev/null" );
            my $scpID = open3( my $scpin, ">&SCPOUT", \*SCPERR,
                "$scpCommand/$file $targetDir/$file"
            );
            # we don't need it
            close $scpin;

            # Some magic for non-blocking input from
            # scp STDERR
            fcntl( SCPERR, F_SETFL,
                fcntl( SCPERR, F_GETFL, 0 ) | O_NONBLOCK
            );

            # let wait our scp
            my $cid;
            while( ! ( $cid = waitpid( -1 , WNOHANG ) ) ) {
                updateDownloadStatus( $targetDir , $file , $expFSize );
                sleep 1;
            }
            print "\n\n";
            my $result = join '' , ( <SCPERR> );
            close SCPERR;
            close SCPOUT;

            if ( $? || ! -e "$targetDir/$file" || -z "$targetDir/$file" ) {
                logMsg($result);

                unlink "$targetDir/$file";

                if ( $result =~ /Host key verification failed/ ) {
                        abort("No host key installed for '$hostname'");
                }
                elsif ( $result =~ /No address associated with hostname/ ) {
                        abort("Invalid hostname '$hostname'");
                }
                elsif ( $result =~ /Permission denied \(publickey/ ) {
                        abort("Permission denied for '$username\@$hostname'");
                }
                elsif ( $result =~ /scp:.*Permission denied/ ) {
                        abort("Access denied to file '$file' on '$hostname'");
                }
                elsif ( $result =~ /No such file or directory/ ) {
                        abort("Invalid directory '$file' on '$hostname'");
                }
                else {
                        $result =~ s/\n/ /g;
                        $result =~ s/\r//g;
                        abort("Transmission Error: $result");
                };

                return 0;
            }
        }
        return 1;
}

sub checkSum {
	my ($fpath, $expected) = @_;

	my $result = ( split( /\s+/, `/usr/bin/sum $fpath` ) )[0];
    return ( $result eq $expected );
}
######################################################################
#
#  Download each of the needed NetMRI versions, untar the
#  package, then run the upgrade script file contained
#  within.
#
sub updateNetMRI
{
    for ( my $i=0; $i < @neededFiles; $i++ ) {

        if ($i > 0) { checkAutoUpdateScript (""); }

        my $file = $neededFiles[$i];
	my $expectSum = $checksums[$i];

	my $isMajor = isMajorUpdate( $file );
	my $targetDir = $isMajor ? "/home/<USER>/Backup" : $tmpDir;
	my $targetFile = "$targetDir/$file";

	unless ( $isMajor &&
		( -e $targetFile ) &&
		( checkSum( $targetFile , $expectSum ) )
	) {
		next unless downloadFile($file, $targetDir, $filesizes[$i]);

		# Compute a check sum and make sure it is the same
		# as what we were told it would be.  In case there
		# was a transmission error that wasn't caught.

		unless ( checkSum( $targetFile , $expectSum ) ) {
            abort("ERROR: Downloaded file $file is corrupt");
        }
	}

	if ( $doAutomatic && $isMajor && ( ! $forceMajor ) ) {
		reportAsMajor( $file , $expectSum );
		next;
	}

	if ( $isMajor ) {
        $typeOfUpdatesAvail{ Major } -= 1;
    } else {
        $typeOfUpdatesAvail{ HotFix } -= 1;
    }

	# Process the upgrade file

        my $status = processUpgradeFile ($targetDir, $file);

	getCurrentVersion();
	sendUpdateResults();

        return 0 if ( $status );

	unlink "$targetDir/$file" ||
            &abort( "Cannot delete file: $targetDir/$file" );
    }

    return 1;
}

######################################################################
#
#  Start skipjack
#
sub startSkipjack
{
    return if $skipjackIsRunning || $skipjack_stopped_by_autoupdate == 0;

    report("+++ $productName is being restarted ...\n");
    # Shut down apache if running before starting skipjack processes
    stopNotifier();
    my $result = `/usr/bin/nohup $skipjack/bin/skipjack.sh start 2>&1 &`;
    logMsg($result);
    abort( "Error starting $productName." ) if ($result !~ /Starting Skipjack/);
    print "\n";
    sleep(10);

    $skipjackIsRunning = 1;
}

######################################################################
#
#  Stop skipjack
#
sub stopSkipjack
{
    return if !$skipjackIsRunning;

	# Log current service status to /var/log/messages to aid troubleshooting
	`/bin/systemctl status skipjack.service | /usr/bin/logger`;

	my $status = `/bin/systemctl show skipjack.service -p ActiveState`;

	if ($status =~ /ActiveState=inactive/) {
		return;
	}

	if ($status =~ /ActiveState=activating/) {
		&abort("$productName services are in the process of starting and cannot be stopped at this time, aborting");
	}

    report( "+++ $productName is being stopped for the upgrade process ...\n" );
    my $result = `/usr/bin/nohup $skipjack/bin/skipjack.sh stop 2>&1`;
	logMsg($result);
    sleep(10);
    $result = `$skipjack/bin/skipjack.sh kill 2>&1`;
	logMsg($result);

	$status = `/bin/systemctl show skipjack.service -p ActiveState`;
	if ($status !~ /ActiveState=inactive/) {
		&abort("Failed to stop $productName, aborting");
	}

    $skipjackIsRunning = 0;
	$skipjack_stopped_by_autoupdate = 1;
}

######################################################################
#
#  Send the given message to the AutoUpdate.log file.
#
sub logMsg
{
    my ($msg) = @_;

    my $timestamp = genTimestamp();

    for my $line (split(/\n/, $msg)) {
        next if $line eq '';
        print LOG "$timestamp $line\n";
    }
}

######################################################################
#
#       Executes the given system command and aborts if the command fails
#
sub doCommand
{
    my ($cmd) = @_;
    system($cmd);
    if ($?) {
        &abort("Script Aborted");
    }
}

######################################################################
#
#       Prints the given message and exits the script
#
sub _abort
{
    my($msg) = @_;
    logMsg($msg);
    print "\n*** $msg ***\n\n";

    system("/bin/rm -fr $tmpDir");

    exit -1;
}

sub updateDownloadStatus {
    my ( $dirname , $filename , $totalSize ) = @_;

    my $curSize = ( stat("$dirname/$filename") )[7];
    my $percentDone = 100 * $curSize / $totalSize;

    printf "+++ Downloading %s: %6d of %d KBytes (%.1f%%)\r",
            $filename, $curSize/1024, $totalSize/1024, $percentDone;
}

sub processUpgradeFile
{
        my ($rootDir, $upgradeFile) = @_;

        report("+++ Processing Upgrade File $upgradeFile");

        if (! -f "$rootDir/$upgradeFile") {
            report("Upgrade File '$upgradeFile' Not Found");
		next;
        }

        my ($checksum) = split(/\s/,`/usr/bin/sum $rootDir/$upgradeFile`);
		$upgradeDetails{checksum} = $checksum;

# Unpack the upgrade package and verify the signature

        my $upgradeTar = "upgrade.tgz";

        doCommand("/bin/rm -f $tmpDir/$upgradeTar");

        report("+++ Checking Digital Signature");
        my $cmd = "/bin/echo 'Nobody Knows What 44813 Stands For' | "
                . "LANG=en_US.UTF-8 /usr/bin/gpg "
                . "     --homedir=/root/.gpg"
                . "     --quiet --batch"
                . "     --no-secmem-warning"
                . "     --passphrase-fd 0"
                . "     --output $tmpDir/$upgradeTar"
                . "     --decrypt $rootDir/$upgradeFile 2>&1";

        my $result = `$cmd`;

        if ( $result =~ /decryption failed: secret key not available/ ) {
                abort( "Decryption Failure: $result" );
        }

        if ($result !~ /Good signature/) {
                my $companyName = NetMRI::Util::AutomationControl::brandLookup("COMPANY_NAME_FULL");
                abort( "Invalid Digital Signature:"
                        . " The given file has not been properly signed"
                        . " by $companyName Please ensure that the correct filename was specified. Failure: $result"
                        );
        }

        report("+++ Unpacking Upgrade File");
        doCommand("cd $tmpDir; /bin/tar xfm $upgradeTar"
                . " --use-compress-program /usr/bin/gzip"
                . " >/dev/null 2>&1");

# Get the upgrade directory that was created by the tar file in case
# the user changed the name of the upgrade package

        my $upgradeDir = `cd $tmpDir; (/bin/tar tfz $upgradeTar 2> /dev/null | /usr/bin/head -1)`;
        chomp($upgradeDir);
        $upgradeDir =~ s|/||g;
	$upgradeDetails{'upgradeName'} = $upgradeDir;

        logMsg("Upgrade Name = $upgradeDir");
        
        # Look for an options file and parse it
	my $auOptRef = parseAUOptions("$tmpDir/$upgradeDir/AUOptions");
		
		if ( exists $auOptRef->{ brand } ) {
			my $cid = NetMRI::Util::AutomationControl::brandLookup( "INTERNAL_COMPANY_ID" );
			if ( $auOptRef->{ brand } != $cid ) {
				abort( "This upgrade build isn't licensed for $cid" );
			}
		}
	

	# Check if a reboot is required when completed
	if( $auOptRef->{'reboot'} ||  -e $reboot_reason_file ) {
		$upgradeDetails{'reboot'} = 1;
	}
# Make sure that the upgrade script exists

        my $upgradeScript = "UpgradeNetMRI.pl";

        if (! -f "$tmpDir/$upgradeDir/$upgradeScript") {
                $upgradeScript = "Diagnostic.pl";
                if (! -f "$tmpDir/$upgradeDir/$upgradeScript") {
                    abort("Upgrade Script Not Found");
                }
        }

# If a DESCRIPTION file exists, display it and see if the user wants
# to apply this patch

        my $doScript = "y";

        if (!$doSilent && -f "$tmpDir/$upgradeDir/DESCRIPTION") {
                print "\n$hr\n";
                print "DESCRIPTION: $upgradeDir\n\n";
                if (open(my $fh, "<", "$tmpDir/$upgradeDir/DESCRIPTION")) {
                    my $description = do {local $/; <$fh>};
                    chomp $description;
                    print "$description\n";
                    close($fh);
                }
                print "$hr\n\n";
                $doScript = askYesNoQuestion( "Do you want to apply this patch?", "y", 1);
                print "\n";
        }


	my $status = 0;
	my $doAll = "n";
	my $doBackup = 0;
	my $doBackground = 0;
	my $forceForeground = 0;
	my $failoverIP;

        if ($doScript ne "y") {
            logMsg("+++ Patch Skipped By User");

        } else {
		if($NetMRIMode eq "master") {
			if(-f "$tmpDir/$upgradeDir/MASTER_ONLY" || $secondaryRole) {
				$doAll = "n";
			} else {
				if($doAutomatic != 1 && !$doSilent) {
					my $productNamePlural = NetMRI::Util::AutomationControl::brandLookup("PRODUCT_NAME_PLURAL");
					my $productNameOC = NetMRI::Util::AutomationControl::brandLookup("PRODUCT_NAME_OC_SHORT");
					$doAll = askYesNoQuestion( "Do you want to apply this patch to all the $productNamePlural in the $productNameOC environment?", "y", 1);
					print "\n";
				} else {
					$doAll = "y";
				}
			}
		}
        my $run_failover_disable = 0;
		if($fo_cfg && $fo_cfg->{local_index} && $fo_cfg->{second_management_ip}) {
			$failoverIP = $fo_cfg->{local_index} == 1 ? $fo_cfg->{second_management_ip} : $fo_cfg->{first_management_ip};
			chomp(my $local_role = `drbdadm role nadata0 | cut -d '/' -f 1`);

			print "+++ Failover configuration is found.\n";
			print "    Failover state:      ".( $failover_enabled && !$failover_needs_enable ? "Enabled" : "Disabled")."\n";
			print "    Local failover role: $local_role \n";
			print "    Remote failover IP:  $failoverIP \n";

			if($failover_enabled){
				# disable failover for the update with restore flag
		        unless ( $failover_needs_enable ) {
                    $run_failover_disable = 1;
					$failover_needs_enable = 1;
				}
				#force update the remote peer
				print "+++ The patch will also be applied on the remote failover peer.\n";
				$doFailoverUpdate = 1;
			} elsif ($doFailoverUpdate && !$doSilent) {
				# ask about upgrade the failover peer unless it is disabled already
				my $answer = askYesNoQuestion("Do you want to apply this patch to remote failover peer?", "y", 1);
				print "\n";
				$doFailoverUpdate = $answer eq 'y' ? 1 : 0;
			}
		}

# Check if package allows/needs backup or should be backgrounded
# If we are in auto mode, take the defaults
		my $allowBackup = (defined $$auOptRef{backup} &&
			$$auOptRef{backup} eq "1");
		my $allowBackground = (defined $$auOptRef{background} &&
			$$auOptRef{background} eq "1");
		$doBackup = $allowBackup unless $skipBackup;
		$doBackground = $allowBackground;

# Override some settings based on mode
		if ($NetMRIMode eq "master" && !$secondaryRole) {
			# Don't background the update on master for now
			$forceForeground = 1;
			if (!$doAutomatic && !$doSilent && $doAll =~ /^y/i) {
				$doBackground = promptBackground(1) if $allowBackground;
			}
			$doBackup = promptBackup() if $allowBackup && !$doAutomatic && !$doSilent;
		} elsif ($NetMRIMode eq "slave") {
			# If we got "-bg" on cmdline, force background mode
			if ($forceBackground) {
				$doBackground = 1;
			} else {
				# If answered "n" to background on OC
				# force slaves run in foreground.
				$doBackground = 0;
			}
		} else {
			# Standalone or secondary master. If we're in interactive mode,
			# ask user what to do, otherwise take defaults
			unless ($doAutomatic || $doSilent) {
				$doBackup = promptBackup() if $allowBackup;
				$doBackground = promptBackground(0) if $allowBackground;
			}
		}
		# Perform the upgrade
        report("+++ Performing Upgrade");
        close (LOG);

		daemonize($doBackground && !$forceForeground) if $allowBackground;

		# Stop skipjack if it is running
        stopSkipjack();

		# Disable failover if it is needed
        if ($run_failover_disable) {
			print "+++ Disabling the failover for system update...\n";
   			doCommand("/home/<USER>/adminShell -c failover disable -s -r -S");
        }

		# Skipjack is down, start up upgrade apache notifier
		if ($NetMRIMode ne "slave") {
			configNotifier($httpPort, $httpsPort);
			startNotifier();
		}

# Nohup and background if needed.
# Currently skip backgrounding on OC, but send through to slaves
# Send -multi if OC (bg) doing collectors. Tells child to save tmp dir.
		my $cmd = "cd $tmpDir/$upgradeDir;";
		$cmd .= " ./$upgradeScript";
		if ($doBackup) {
			$cmd .= " -backup";
			# if remote backup is chosen, pass its params along
			$cmd .= "=$doBackup" if ($doBackup =~ /DELIMITER/);
		}
		#$cmd .= " -multi" if ($doAll =~ /^y/i);
		if ($doBackground && !$forceForeground) {
			$cmd .= " -bg \"$bgDir\"";
		}

		# approximate start time
		$upgradeDetails{'start'} = time();

		# Run in foreground, block Ctrl-C
		my $oldINT  = $SIG{'INT'};
		$SIG{'INT'} = 'IGNORE';
		system($cmd);
		$status = $?;

		writeDaemonExitFile($status);

		$upgradeDetails{'status'} = $status;
		$upgradeDetails{'end'} = time();
		$SIG{'INT'} = $oldINT;

		open (LOG, ">>$logFile");

# Append the status to the UpgradeHistory.log

		my $timestamp = genTimestamp();
		appendUpgradeLog("$timestamp  $upgradeDir  checksum = $checksum  status = $status\n");

	}
	
	if ($status == 0 && $doAll eq "y") {
		updateNetMRIs("$rootDir", "$upgradeFile", $doBackground, $doBackup);
	} else {
	
		# if an oc, update upgrade collector status table with status of bypassed to allow update to complete
        # skip this for the secondary
		if ($NetMRIMode eq 'master' && !$secondaryRole) {

			# open sql connection
			sqlOpen("root", $mysqlPass);
			if ($main::sqlError) {
				logMsg("Error connecting to upgrade collector status table: $main::sqlError.");
			}

			# execute the update command to update all collectors to status of bypassed
			sqlExecute("update netmri.upgradeCollectorStatus set upgrade_status = 'bypassed'");
			if ($main::sqlError) {
				logMsg("Error updating collector bypassed status table: $main::sqlError.");
			} else {
				logMsg("Updated all collectors with the upgrade status of bypassed.");
			}
		}	
	}

	# update failover pair if necessary
	if($doScript eq "y" && $doFailoverUpdate && $failoverIP) {
		# perform the failover peer upgrade in background, to avoid the roles switch
		updateFailoverNode("$rootDir", "$upgradeFile", 1, $doBackup, $failoverIP);
	}

	# Clean up tmp files here unless doing background update
    doCommand("/bin/rm -fr $tmpDir/$upgradeTar $tmpDir/$upgradeDir");

	#NETMRI-18921; the upgrade script now dynamically determines the need to reboot so
	#              we needed a mechanism to set this after UpgradeNetMRI.pl runs. If
	#              we are in the background, we just 'reboot'
	if(-e $rebootFile || -e $reboot_reason_file) {
	    $upgradeDetails{'reboot'} = 1;
    }
    return $status;
}

sub genTimestamp
{
	my ($mon,$day,$year,$hour,$min,$sec) = (localtime())[4,3,5,2,1,0];
	$mon += 1; $year += 1900;
	sprintf("%04d-%02d-%02d %02d:%02d:%02d", $year, $mon, $day, $hour, $min, $sec);
}

sub appendUpgradeLog
{
    my ($msg) = @_;

    open (UPGRADELOG, ">>$upgradeLog") || abort("Unable to append $upgradeLog");
    print UPGRADELOG $msg;
    close (UPGRADELOG);
}

#
# If the AutoUpdate script has changed, then execute the new
# script to complete the upgrade process
#
sub checkAutoUpdateScript
{
    my $diff = `/usr/bin/diff /home/<USER>/AutoUpdate $tmpDir`;
    if ($diff ne "") {
        report($AutoUpdateMsg);
        startSkipjack();
        close (LOG);
        unlink("/var/run/netmri.autoupdate.pid");
        system("/home/<USER>/AutoUpdate @ARGV");
        exit;
    }
}

sub report
{
    my($msg) = @_;
    logMsg($msg);
    print "$msg\n";
}

sub getNetMRIs {
	my $admsh = `echo "stats:y" | sudo /home/<USER>/Configure tunserver set`;
	my @lines = split(/\n/,$admsh);
	my @headers = ();
	my @disconnectedNetMRIs = ();
	foreach my $line (@lines) {
		next if $line =~ /^$/;  # Skip blanks
		if ($line =~ /^"UnitSerial/) {
			$line =~ s/"//g;
			@headers = split(',', $line);
			next;
		}
		my @cols = split(',', $line);

		# Map values
		my %row;
		foreach my $header (@headers) {
			my $value = shift @cols;
			$value =~ s/"//g;
			$row{$header} = $value;
		}

		if ($row{Status} !~ /Connected Since/) {
			push(@disconnectedNetMRIs, $row{UnitIPAddress});
		}
	}

	my $db = new NetMRI::Sql(
		DBType	=> "mysql",
		DBName	=> "netmri",
		DBUser	=> "root",
		DBPass	=> $mysqlPass,
		DBHost	=> $mysqlHost,
		DBPort	=> "3306"
	);
	my $query = "select UnitID, UnitIPAddress, "
		. "NetmriDecrypt(UnitPasswordSecure, 'cliCredential', "
		. "SecureVersion) as UnitPassword, UnitName "
		. "from netmri.UnitRegistry where UnitType = 'NetMRI'";
	if (@disconnectedNetMRIs > 0) {
		$query .= " and UnitIPAddress not in ('" . join("','", @disconnectedNetMRIs) . "')";
	}
	$query .= " order by UnitID";

	my @rows = $db->table($query);

    return(@rows);
}

sub checkUnitsForExpiration {
	my ( $units, $timefield ) = @_;

	my $now = time();
    my $tf;
	for my $unit ( @$units ) {
        $tf = $$unit{ $timefield };
        if ($tf) {
            $$unit{ Expired } = Date::Parse::str2time( $tf ) <= $now;
            $$unit{ Unknown } = 0;
        } else {
            $$unit{ Expired } = 0;
            $$unit{ Unknown } = 1;
        } 
	}

	return $units;
}

sub checkUnitsForReachability {
	my $units = shift;

	my $ping = Net::Ping->new("icmp",5);

    for my $unit ( @$units ) {
        $$unit{ Ping } = $ping->ping( $$unit{ UnitIPAddress } )
	}

	$ping->close();

    return $units;
}

sub callUnitsLicenseInfoAPI {
	my $units = shift;

        for my $unit ( @$units ) {
                my $resp = eval {
                    NetMRI::API->new( {
                            username    => 'admin', 
                            password    => $unit->{UnitPassword}, 
                            url         => 'http://'. $unit->{UnitIPAddress},
                            api_version => 2.5,
                    } )->api_request( license_info => {} );
                };

                if ( my $error = $@ ) {
                    $$unit{ LicenseAPIException } = $error;
                } else {
                    my $content = $resp->content();
                    @$unit{ keys %$content } = values %$content;
		    if ($content->{maintenance_expiration} eq "") {
			$$unit{ LicenseAPIException } = "Invalid empty maintenance_expiration"
		    }
                }
        }

	return $units;
}

sub callUnitsLicenseInfoCLI {
        my $units = shift;
        my %varsMap = ( maintenance_expiration =>
            qr/Maintenance Expires:\s*(\d{4}-\d{2}-\d{2})/
        );

        my $res;
        my ( $v, $rex );
        for my $unit ( @$units ) {
            $res = `/home/<USER>/ShowCollectors $$unit{UnitID} license`;
            @$unit{ @_ } = map {
                if( ( exists $varsMap{ $_ } ) && ( $res =~ $varsMap{ $_ } ) ) {
                    $1;
                } else {
                    "";
                }
            } @_
        }

        return $units;
}

sub updateNetMRIs {
	my ($rootDir, $theFile, $doBackground, $doBackup) = @_;

	if($NetMRIMode ne "master" || $secondaryRole) {
		return;
	}
    logMsg("+++ Collectors Update ...");

	my @netmris = getNetMRIs();

	foreach my $netmri (@netmris) {
		
        my $ipAddress = $netmri->{UnitIPAddress};
		my $password = $netmri->{UnitPassword};

		# Go ahead and check ping and scp for IPv6
		# in case we use over vpn in future
		my $isV6 = ($ipAddress =~ /:/ ? 1 : 0);
		my $pCmd = '/bin/ping';
		my $scpIP = $ipAddress;
		if ($isV6) {
			$pCmd .= '6';
			$scpIP = '[' . $ipAddress . ']';
		}

		# Make sure the collector is reachable first
		# Send 1 ping and wait up to 6 secs for reply
		system("$pCmd -w6 -n -c1 $ipAddress >/dev/null 2>&1");
		if ($? != 0) {
			report("+++ Skipping host $ipAddress (unreachable)");
			next;
		}

        	my $sc = new NetMRI::SecureChannel(
			machine => $netmri->{UnitIPAddress},
			user => 'admin',
			password =>  $netmri->{UnitPassword},
			keyFile => '/etc/openvpn/id_rsa',
			logStdOut => 1,
			timeout => undef,
			quiet => 0
		);
		
		report("+++ Copying $theFile to $ipAddress");
		my ($result, $exstat) = $sc->sendFile("$rootDir/$theFile");
		if($result) {
			report("+++ OK (Copying $theFile to " . $ipAddress . ")");
		} else {
			report("+++ FAILED (Copying $theFile to " . $ipAddress . ")");
            updateCollectorStatus($ipAddress, "failed");
			next;
		}
		
		report("+++ Installing $theFile on $ipAddress");
		my $auCmd = 'autoupdate ';
		$auCmd .= ($doBackground ? '-bg ' : '-silent ');
		if ($doBackup) {
			# NETMRI-23510 -backup is not currently an option
			# Also, This will cause input validation aborts.
			# Leave in comments for now
			#$auCmd .= "-backup ";
			# if remote backup is chosen, pass its params along
			#$auCmd .= "=$doBackup " if ($doBackup =~ /DELIMITER/);
		} else {
			$skipBackup = 1;
            $auCmd .= "-skipbackup ";
        }
		$auCmd .= $theFile;
        ($result, $exstat) = $sc->execute($auCmd);
		if($result) {
			report("+++ OK (Installing $theFile on " . $ipAddress . ")");
		} else {
			report("+++ FAILED (Installing $theFile on " . $ipAddress . ")");
            updateCollectorStatus($ipAddress, "failed");
		}
	}
}

sub updateFailoverNode {
	my ($rootDir, $theFile, $doBackground, $doBackup, $ipAddress) = @_;

	logMsg("\n+++ Update failover node $ipAddress...");

	# Go ahead and check ping for IPv6
	my $pingCmd = $ipAddress =~ /:/ ? '/bin/ping6' : '/bin/ping';

	# Make sure the IP is reachable first: send 1 ping and wait up to 6 secs for reply
	system("$pingCmd -w6 -n -c1 $ipAddress >/dev/null 2>&1");
	if ($? != 0) {
		report("+++ Unable to upgrade $ipAddress (unreachable)");
		return;
	}

	my $rsa_file = '/home/<USER>/.ssh/id_drbd_rsa';
	unless(-e $rsa_file){
		report("+++ Unable to upgrade $ipAddress RSA key is missing");
		return;
	}
	my $sc = new NetMRI::SecureChannel(
		machine => $ipAddress,
		user => 'admin',
		keyFile => $rsa_file,
		logStdOut => 1,
		timeout => undef,
		quiet => 0
	);

    report("+++ Copying $theFile to $ipAddress");
    my ($result, $exstat);
    ($result, $exstat) = $sc->sendFile("$rootDir/$theFile"); 
    unless($result) {
        report("+++ FAILED (Copying $theFile to " . $ipAddress . ")");
        return;
    }
    report("+++ OK (Copying $theFile to " . $ipAddress . ")");

	report("+++ Installing $theFile on $ipAddress");
	# use -skipfailover to break the recursion, tell the partner not update us
	my $auCmd = 'autoupdate -skipfailover '; 
	$auCmd .= ($doBackground ? '-bg ' : '-silent ');
	unless ($doBackup) {
		$auCmd .= "-skipbackup ";
	}
	$auCmd .= $theFile;
	($result, $exstat) = $sc->execute($auCmd);

	if($result) {
		report("+++ OK (Installing $theFile on " . $ipAddress . ")");
	} else {
		report("+++ FAILED (Installing $theFile on " . $ipAddress . ")");
	}
}

sub promptBackup
{
	my $doBackup = 0;

	return $doBackup if $NetworkName eq '';

	if (!$doAutomatic && $NetMRIMode ne 'slave') {
		my $flush = $|;
		$| = 1;

		my $companyName = NetMRI::Util::AutomationControl::brandLookup("COMPANY_NAME");
		print "\n******************************************************************\n";
		print "*                                                                *\n";
		print "*   $companyName recommends making a backup of the existing network  *\n";
		print "*          database before proceeding with the upgrade           *\n";
		print "*                                                                *\n";
		print "******************************************************************\n";

		my $dir = '/home/<USER>/Backup/';
		my $fname_begin = $NetworkName.'_'.$SerialNo;
		opendir(my $dh, $dir);
		my %files = map { $_ => (stat("$dir/$_"))[10] } grep(/^$fname_begin.*\.tgz$/, readdir($dh));
		closedir($dh);
		my @sorted_files = sort { $files{$b} <=> $files{$a} } (keys %files);
		my $dbFile = $dir.$sorted_files[0];
		
		if ($sorted_files[0] && -e $dbFile) {
			my ($ftime) = (stat($dbFile))[10];
			my ($mon, $day, $year, $hour, $min, $sec) = (localtime($ftime))[4,3,5,2,1,0];

			my $buf = sprintf("%04d-%02d-%02d %02d:%02d:%02d", $year+1900,$mon+1,$day,$hour,$min,$sec);
			logMsg("The current network database backup was made $buf.");
		} else {
			logMsg("There currently is no network database backup.");			
		}
		
		my $ans = askYesNoQuestion("\nDo you want to create a network backup?", "y", 1);
		if ($ans =~ /^n/i) {
			print "\n";
			logMsg("***** WARNING. You have selected to not proceed with the backup. *****");
			print "\nAre you sure you want to skip the backup? (y/n) [n]: ";
			my $ans = <STDIN>; chomp($ans);
			if ($ans =~ /y/i) {
				logMsg("***** User selected to skip the backup");
			} else {
				$doBackup = 1;
			}
		} else {
			$doBackup = 1;
		}

		if ($doBackup) {
			print "\nDo you want to put backup on local or remote host? (local/remote) [local]: ";
			chomp(my $backupType = <STDIN>);
			while ($backupType && $backupType !~ /^(local|remote)$/i) {
				print "\nPlease, choose one of the following (local/remote) [local]: ";
				chomp($backupType = <STDIN>);
			}
			$backupType ||= 'local';
			logMsg("***** User selected $backupType backup");
			if ($backupType =~ /remote/) {
				my $input = '';
				my $dest = ($AutoCopyDest1 || $AutoCopyDest2);
				$dest = decode_base64($dest) if $dest !~ /,DELIMITER,/;
				my ($rUser, $rPass, $rHost, $dated, $rDir, $rUseSSHKeys) = split ',DELIMITER,', $dest;
				$dated ||= 'on';
				print "Remote username [$rUser]: ";
                chomp($input = <STDIN>);
				$rUser = $input || $rUser;

				if (! -f "/home/<USER>/.ssh/id_rsa.remote") {
					$rUseSSHKeys = "off";

					print "Remote password [$rPass]: ";
					ReadMode 2;
					$input = <STDIN>;
					print "\n";
					ReadMode 0;
					chomp $input;
					$rPass = $input || $rPass;
				} else {
					my $useKeys = "n";
					if (lc($rUseSSHKeys) eq "on") {
						$useKeys = "y";
					}
					print "Use SSH Keys? (y/n) [$useKeys]: ";
                    chomp($input = <STDIN>);
					$useKeys = $input || $useKeys;

					if ($useKeys =~ /^n/i) {
						$rUseSSHKeys = "off";
						print "Remote password [$rPass]: ";
						ReadMode 2;
						$input = <STDIN>;
						print "\n";
						ReadMode 0;
						chomp $input;
						$rPass = $input || $rPass;
					} else {
						$rUseSSHKeys = "on";
						$rPass = "";
					}
				}
				print "Remote host [$rHost]: ";
                chomp($input = <STDIN>);
				$rHost = $input || $rHost;
                while ($rHost eq '') {
                    print "\nPlease, input Remote host []: ";
                    chomp($input = <STDIN>);
                    $rHost = $input || $rHost;
                }
                print "Remote directory [$rDir]: ";
                chomp($input = <STDIN>);
                $rDir = $input || $rDir;
                while ($rDir eq '') {
                    print "\nPlease, input Remote directory []: ";
                    chomp($input = <STDIN>);
                    $rDir = $input || $rDir;
                }
                $rDir =~ s,/+,/,g;
                $rDir =~ s,^/,,g;
                $rDir =~ s,/$,,g;
                $rDir = $rDir eq '' ? '/' : "/$rDir/";
				logMsg("***** Backup files to be copied to $rUser\@$rHost:$rDir");
				my $remote = join ',DELIMITER,', ($rUser, $rPass, $rHost, $dated, $rDir, $rUseSSHKeys);
				$doBackup = NetMRI::Util::Format::cmdLineProtect($remote);
			}
		}
		$| = $flush;
	} else {
		# Automode always does backup
		$doBackup = 1 unless $skipBackup;
	}

	return $doBackup;
}

sub promptBackground
{
	my $doOC = shift || 0;
	my $doBackground = 1;

	my $flush = $|;
	$| = 1;

	my $ans = "n";
	if ($doOC) {
		$ans = askYesNoQuestion("\nDo you want to run collector updates in the background?", "y", 1);
	} else {
		$ans = askYesNoQuestion("\nDo you want to run this update in the background?", "y", 1);
	}
	if ($ans =~ /^n/i) {
		logMsg("***** User selected to run update in the foreground");
		$doBackground = 0;
	}
	$| = $flush;

	return $doBackground;
}


# Configure Apache to display a notice when Skipjack is down
sub configNotifier
{
	my ($httpPort, $httpsPort) = @_;

	my $NotifierDir = "/var/local/netmri/notifier";
	my $sedCmd = "/bin/sed";
	$sedCmd .= " -e 's|__NOTIFIER_DIR__|$NotifierDir|g'";

	if ($httpPort eq "" || $httpPort eq "NONE") {
		$sedCmd .= " -e 's|__HTTP_PORT__|80|g'";
	} else {
		$sedCmd .= " -e 's|__HTTP_PORT__|$httpPort|g'";
	}
	if ($httpsPort eq "" || $httpsPort eq "NONE") {
		$sedCmd .= " -e 's|__HTTPS_PORT__|443|g'";
	} else {
		$sedCmd .= " -e 's|__HTTPS_PORT__|$httpsPort|g'";
	}

	#NETMRI-27347; Never update notifier on grid member (NI)
	unless(NetMRI::Util::HW::isAutomationGridMember()){

		# Use showUpgrade.html during updates
		$sedCmd .= " -e 's|showMessage.html|showUpgrade.html|g'";
		
		system("$sedCmd /etc/netmri/httpd/httpd.conf.notifier "
				.  " >/etc/netmri/httpd/httpd.conf.notifier.active");
		# re-point the sym link to the httpd.conf.notifier
		unlink("/etc/netmri/httpd/httpd.conf");
		symlink("/etc/netmri/httpd/httpd.conf.notifier.active","/etc/netmri/httpd/httpd.conf");
	}
		
	return 0 if ($?);

	# NETRMI-17213: create sym link to allow for the sandbox to see repomd.xml file
	symlink("/tools/skipjack/app/netmri","/var/local/netmri/notifier/netmri");
			
	return 0 if ($?);

	my $passwdFile = "/etc/httpd/conf/passwd";
	system("/bin/rm -f $passwdFile");
	return 0 if ($?);
	return 1;
}

# Start the notifier
sub startNotifier
{
	my $ret = 1;

	my $result = `/bin/systemctl start httpd.service 2>/dev/null`;
	$ret = 0 if ($result =~ /FAILED/);

	sleep 5;
	return $ret;
}

# Stop the notifier
sub stopNotifier
{
	my $ret = 1;

	my $result = `/bin/systemctl stop httpd.service 2>/dev/null`;
	$ret = 0 if ($result =~ /FAILED/);
	return $ret;
}

# Clean apache conf files
sub cleanNotifier
{
	my $ret = 1;

	sleep 5;
	
	# reset the symlink
	unlink("/etc/netmri/httpd/httpd.conf");
	symlink("/etc/netmri/httpd/httpd.conf.gui","/etc/netmri/httpd/httpd.conf");
	
	$ret = 0 if ($?);
	
	# NETMRI-17213: remove sym link that allows sandbox so see the repomd.xml file.
	unlink ("/var/local/netmri/notifier/netmri");
			
	return 0 if ($?);

	return $ret;
}

######################################################################
#
#  updateCollectorStatus
#
sub updateCollectorStatus
{
	# read in the colllector ip address and status
	my ($ipAddress, $status) = @_;

	# open sql connection, mysql password being loaded by loadCfgFile
	sqlOpen("root", $mysqlPass);
	if ($main::sqlError) {
		logMsg("Error connecting to upgrade collector status table: $main::sqlError.");
	}

	# execute the update command
	sqlExecute("update netmri.upgradeCollectorStatus set upgrade_status = '$status' where ip_address = '$ipAddress'");
	if ($main::sqlError) {
		logMsg("Error updating collector status table: $main::sqlError.");
	} else {
		logMsg("Updated $ipAddress collector with the upgrade status of $status.");
	}
}

sub checkUpdateIsRunning
{
	my $running;
	my $daemon = Proc::Daemon->new();
	my $daemon_status = $daemon->Status($daemon_pid_file);

	$running = NetMRI::Util::Process::createPidFile("/var/run/netmri.autoupdate.pid", 'AutoUpdate') unless($daemon_status);

	if($daemon_status || ($running != $$)) {
		print "\n***** ERROR: Autoupdate process is already running *****\n";
		exit 1;
	}

	# Check to make sure an upgrade not in progress
	my $upgradePidFile = "/var/run/netmri.UpgradeNetMRI.pid";
	if (open(my $pfh, $upgradePidFile)) {
		my $pid = 0;
		my $check = 0;
		while (<$pfh>) {
			if (/^(\d+)$/) {
				$pid = $1;
				last;
			}
		}
		close $pfh;

		# See if process still around
		$check = kill 0, $pid if ($pid);

		# If process still there, bail....
		if ($pid && $check) {
			print "\n***** ERROR: A system upgrade is in progress *****\n";
			exit 1;
		}
		unlink $upgradePidFile;
	}
	return $running;
}

# Quietly cleanup old unpackaged updates 
sub cleanTmp {
	return unless($doCleanTmp);
	my $tmp = '/home/<USER>/chroot-home/tmp';
	foreach(glob "$tmp/*") {
		if(my ($tmp_pid) = /\Q$tmp\E\/(\d+)$/) {
			# Don't remove if it is not a directory
			next unless(-d "$tmp/$tmp_pid");

			if($tmp_pid != $auPid) {
				# Remove if the pid is not currently running
				next if(-e "/proc/$tmp_pid");
				# Don't remove if it was created after this process started
				next if(-M "$tmp/$tmp_pid" < 0);
			}
			rmtree("$tmp/$tmp_pid");
		}
	}
}


sub writeDaemonExitFile {
    my ($status) = @_;

	return if $status || !$daemonized;

	open my $fh,'>', $daemon_exit_file;
	print $fh '1';
	close $fh;
}

sub daemonize {
	my ($doBackground) = @_;

	# NETMRI-25393 NetMRI::SecureChannel has issues interfacing with tail -f
	# Foreground and slave will not daemonize
    # NETMRI-27080: foreground and if it was run from failover primary will not daemonize also
	return if ( $NetMRIMode eq "slave" || $noDaemonize ) && !$doBackground;

	# Must disable END block before daemon init  
	$enableENDBlock = 0;

	my $daemon_output_log = "/home/<USER>/chroot-home/tmp/daemon_output_log";
	open my $fh, '>', $daemon_output_log;
	close $fh;

	my $daemon_pid = Proc::Daemon::Init({
		work_dir => getcwd(),
		pid_file => $daemon_pid_file,
		child_STDOUT => $daemon_output_log,
		child_STDERR => $daemon_output_log
	});

	$daemonized = 1;

	if($daemon_pid) {
		unless($doBackground) {
			system("/usr/bin/tail","--pid=$daemon_pid","-f","$daemon_output_log");
			if(-e $daemon_exit_file) {
				exit 0;
			} else {
				exit 1;
			}
		} else {
			print "Use \"show updatelog\" to check status\n";
			exit(0);
		}
	} else {
		$enableENDBlock = 1;
	}
}

END {
	my $retcode = $?;
	finalize();
	$? = $retcode;
	return unless $enableENDBlock;

	cleanTmp();
	removePidFile();
	unlink $daemon_pid_file;
	# Clean up reboot file just in case
	unlink $rebootFile;
	system("/bin/sync");
}
