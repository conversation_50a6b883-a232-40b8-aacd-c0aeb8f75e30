module ApplicationHelper
  include SqlDecoder

  def t (*args)
    return translate(*args)
  end

  def l (*args)
    return localize(*args)
  end

  def translate (message, opts = {})
    return message
  end

  def localize (message, opts = {})
    return message
  end

  def page_title
    "#{Brand.lookup(:COMPANY_NAME)} #{Brand.lookup(is_oc ? :PRODUCT_NAME_OC_ONLY : :PRODUCT_NAME_ONLY)} - #{sw_version}"
  end

  def is_oc
    return (GlobalSetting.new).net_mri_mode == 'master'
  end

  def sw_version
    GlobalSetting.new.server_cfg[:Version] || 'Unknown'
  end

  def vendor_data
    d = Device.find(:all, :select => "distinct DeviceVendor")
    a = []
    d.each do |item|
      if (!item.DeviceVendor.nil?)
        a << [item.DeviceVendor, item.DeviceVendor]
      end
    end
    a
  end

  def netmri_header
    content_tag(:div, render(:partial => "shared/header"), :id => 'netmri_header')
  end

  def netmri_footer
    content_tag(:div, :id => 'netmri_footer') do
      content_tag(:span) do
        Brand.lookup(:COMPANY_COPYRIGHT)
      end +
        content_tag(:div, '', :id => 'netmri_time')
    end
  end

  def process_content
    if main_pages?
      netmri_header +

        content_tag(:div, :style => 'display: none') do
          yield
        end +

        netmri_footer
    else
      yield
    end
  end

  def network_type_data
    serverCfg = ServerCfg.new
    nt = (serverCfg.setting(:NetworkTypes)).gsub(/'/, '')
    arr = nt.split(/,/)
    a = []
    arr.each do |item|
      a << [item, item]
    end
    a
  end

  def device_groups_tree_data(timestamp = nil, short_network_ids_list = nil)
    #    current_user = AuthUser.find_by_user_name('admin')
    ids = current_user.get_privilege_device_groups('view_non_sensitive')
    dgs = DeviceGroup.by_device_count_with_group_ids(ids).all(:order => 'Network ASC, GroupName ASC', :timestamp => timestamp)
    networks = DeviceGroup.networks_for_groups(ids, timestamp)
    total_count = Device.count_with_user_name(current_user.user_name, 'view_non_sensitive')
    if short_network_ids_list.nil?
      setup_device_group_tree_data(dgs, total_count)
    else
      setup_device_group_tree_data(dgs, total_count, :short_network_ids_list => true)
    end
  end

  def device_group_with_device_count_data
    ids = current_user.get_privilege_device_groups('view_non_sensitive')
    dgs = DeviceGroup.by_device_count_with_group_ids(ids).all(:order => 'Network ASC, GroupName ASC')
    total_count = Device.count_with_user_name(current_user.user_name, 'view_non_sensitive')
    last = ""
    a = []
    dgs.each do |dg|
      a << [dg.GroupID, "#{dg.GroupName} (#{dg.device_count})"]
    end
    a.insert(0, [0, "#{Brand.lookup(:ALL_DEVICES)} (#{total_count})"])
  end

  def device_group_selector_data(timestamp = nil)
    ids = current_user.get_privilege_device_groups('view_non_sensitive')
    dgs = DeviceGroup.by_device_count_with_group_ids(ids).all(:order => 'Network ASC, GroupName ASC', :timestamp => timestamp)
    a = Array.new
    dgs.each do |dg|
      a << [dg.Network, false, dg.GroupID, dg.GroupName, "#{dg.GroupName} (#{dg.device_count})", dg.device_count]
    end
    table = current_user.device_group_table
    total_count = 0

    unless table.blank?
      total_count = ActiveRecord::Base.connection.select_all("select count(*) as total from #{table}")
      total_count = total_count.first['total'].to_i
    else
      total_count = Device.count
    end
    all_name = Brand.lookup(:ALL_DEVICES)
    a.insert(0, ["", true, 0, all_name, "#{all_name} (#{total_count})", total_count])
  end

  def issue_selector_data
    # get normal issues first
    [['0', 'All Issues', 'Issues']] + IssueDesc.issue_selector_data
  end

  def issue_selector_fields
    return ["IssueTypeID", "Title", "Component"]
    # return ["Component", "Context", "Correctness", "Description", "IssueDescID",
    # "IssueType", "IssueTypeID", "PriorityID", "SeverityID", "Stability",
    # "Title"]
  end

  def interface_group_selector_data
    # get normal issues first
    fields = interface_group_selector_fields
    issues = IfGroup.all(:order => 'GroupName')
    items = issues.collect do |row|
      fields.collect do |key|
        if key == 'Timestamp'
          row[key].to_s
        elsif key == 'GroupName'
          "#{row[key]} (#{row.MemberCount})"
        else
          row[key].to_s
        end
      end
    end
    total = Interface.count_with_user_name(current_user.user_name, 'view_non_sensitive')
    items.insert(0, [0, "All Interfaces (#{total})", total]) # has to match order of fields
    items
  end

  def interface_group_selector_fields
    return ['GroupID', 'GroupName', 'MemberCount']
    # return ["Criteria", "FlowCollection", "GroupID", "GroupName", "MemberCount", "Rank", "SNMPPolling", "Timestamp"]
  end

  def time_window_data
    items = TimeWindow.all
    items.collect do |row|
      [row.id, row.schedule_name]
    end
  end

  def notification_defaults
    settings = Hash.new
    records = AdvSetting.with_adv_setting_def.find(:all,
      :select => "adv_settings.id, adv_settings.adv_setting_def_id, adv_settings.value as value, adv_setting_defs.id, adv_setting_defs.name, adv_setting_defs.setting_type, adv_setting_defs.category, adv_setting_defs.visible, adv_setting_defs.description, adv_setting_defs.default_value, adv_setting_defs.allow_empty, adv_setting_defs.feature, adv_setting_defs.display_hints, adv_setting_defs.ui_name",
      :conditions => ["adv_setting_defs.category in ('Notification', 'ChangeNotification', 'IssueNotification', 'JobNotification', 'SystemNotification') AND adv_setting_defs.setting_type != 'password'"])
    records.each { |y| settings[y.name] = y.value }
    settings
  end

  def user_privileges(user)
    if user.class == String
      user = AuthUser.find_by_user_name(user)
    end
    user.privileges
  end

  # Display the name of the network in the header
  def network_name
    s = ServerCfg.new
    network_name = s.setting(:NetworkName)
  rescue e
    "ERROR: #{e.message}"
  end

  UPDATE_TYPE2DESCRIPTION = {
    HotFix: 'HotFix',
    Major: 'Major Update'
  }

  def parse_update_description(up_descr)
    descr_arr = up_descr.split(",")
    if not descr_arr.empty?
      descr_arr.map! do |ditem|
        item_arr = ditem.split(":")
        pluralize(item_arr[1].to_i, UPDATE_TYPE2DESCRIPTION[item_arr[0].to_sym])
      end
      "Update Available: " + descr_arr.join(" ")
    end
  end

  # Display an update notification if updates are available.
  def update_notification
    # TODO - swhite - logic needed
    # jcamp - Logic:
    #   added 20090813 - if maintenanceExpiration is blank, display license upgrade message
    #   if there is an update available, display that message (the teaser)
    #   elsif maintenance has expired, then display that message (the wake-up)
    #   else display the customer name from the license file (the default)
    freeVersion = false
    update_available = false
    maintenance_expired = false
    maintenance_blank = false
    maintenance_invalid = false

    s = ServerCfg.new
    # The comparison below fails if you set t1 to todays date.
    # (I.e., maintenance says it is expired if it expires today.)
    # So set current time to yesterday before the comparison.
    t1 = -1.day.from_now

    if ((!s.setting(:MaintenanceExpiration).nil?) && (!s.setting(:MaintenanceExpiration).blank?))
      begin
        t2 = Time.parse(s.setting(:MaintenanceExpiration))
        if ((t2 <=> t1) == -1)
          maintenance_expired = true
        end
      rescue => e
        maintenance_invalid = true
      end
    else
      # Set maintenance_expired to false for now until we decide whether or not to force new licenses.
      maintenance_expired = false
      maintenance_blank = true
    end

    # Check to see if this is the free version.
    if (!s.setting(:EffectiveDeviceLicenseLimit).nil? && !s.setting(:LicenseType).nil? &&
        s.setting(:EffectiveDeviceLicenseLimit) == "25" && s.setting(:LicenseType) == "Eval")
      freeVersion = true
    else
      freeVersion = false
    end

    # Check if auto updates are available
    # Check AutoUpdateAvail server.cfg flag (set in AutoUpdate)
    if (s.setting(:AutoUpdateAvail).nil? || s.setting(:AutoUpdateAvail) != "true")
      update_available = false
    else
      update_available = true
    end

    if freeVersion
      "<font class=buynowLink><a href=#{Brand.lookup(:URL_PURCHASE)} target=_new>[ Buy #{Brand.lookup(:PRODUCT_NAME)} Now ]</font></a></font>"
    elsif maintenance_blank
      '4.0 License Upgrade Required'
    elsif update_available
      default_message_text = 'Update Available'
      update_description = s.setting(:AutoUpdateDescription)

      if not update_description.nil?
        parse_update_description(update_description) || default_message_text
      else
        default_message_text
      end
    elsif maintenance_expired
      'WARNING: Software Maintenance Has Expired'
    elsif maintenance_invalid
      'WARNING: Software Maintenance Expiration is invalid'
    else
      customer_name = s.setting(:CustomerName)
      customer_name
    end
  rescue => e
    "Error determining update notification: #{e.message}"
  end

  # This is a custom path generator for Netcordia ExtJS libraries that we modify path
  # TODO: REMOVE THIS once application.html.erb has been changed over
  # This is a custom path generator for Netcordia ExtJS path
  def extjs_path(source)
    compute_public_path(source, 'ext', 'js')
  end

  def extmod_path(source)
    compute_public_path(source, 'ext-mods', 'js')
  end

  def cache_file_name(name, local = false)
    fn = %W(cached #{name})
    fn.concat [params[:controller], params[:action]] if local
    fn.push (ApiInfo.netmri_version_number || 'unknown').gsub('.', '_')

    fn.join '_'
  end

  # the rails equiv of Netcordia.application.compute_exact_time_period in application.js
  def compute_exact_time_period(timestamp, time_period)
    @start_time = Time.now
    @end_time = Time.now
    if time_period && timestamp
      analysis_date = Time.parse(timestamp)
      case time_period
      when 'Daily'
        @start_time = analysis_date
        @end_time = @start_time + 1.day
      when 'Weekly' # Sunday to sunday
        @start_time = analysis_date.beginning_of_week - 1.day
        @end_time = @start_time + 7.days
      when 'Monthly'
        @start_time = analysis_date.beginning_of_month
        @end_time = @start_time.end_of_month.beginning_of_day + 1.day
      when '7-Day'
        @start_time = analysis_date - 6.days
        @end_time = analysis_date + 1.day
      when '30-Day'
        @start_time = analysis_date - 29.days
        @end_time = analysis_date + 1.day
      end
    end
    return [@start_time, @end_time]
  end

  def csrf_token
    token = SecureRandom.hex(32)
    session[:csrf_token] = token
    return token
 end

  def js(arg)
    arg.to_js
  end

  def is_vrf_aware?(device_id)
    InfraDevice.find_by_DeviceID(device_id).try(:VirtualNetworkingInd) || false
  end

  private

  def main_pages?
    main_tab_controllers = ['configuration_management', 'network_explorer', 'dashboard', 'network_analysis', 'report_gallery', 'access']

    main_tab_controllers.include?(params[:controller]) && params[:action] == 'index'
  end

  def stub_multi_device_groups
    dgs = YAML::load(File.read("#{RAILS_ROOT}/spec/fixtures/device_groups/multi.yml"))
    # needs this to fake out data
    dgs.each do |dg|
      class << dg
        def method_missing(m, *args, &block)
          super unless (m =~ /\w+/)
          self[m.to_s]
        end
      end
    end
  end
end
