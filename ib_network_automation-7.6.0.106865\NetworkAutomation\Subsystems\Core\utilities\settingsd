#!/usr/bin/ruby

#-------------------------------------------------------------------------------#
#                                                                               #
# This service will eventually be converted to a native C / XPath filter.       #
# Due to time constraints, the ruby code was reused & shelled out.              #
#                                                                               #
# <PERSON><PERSON> <<EMAIL>>                                           #
# July 19, 2013                                                                 #
#                                                                               #
# Note; execution starts at the bottom of the script.                           #
#                                                                               #
#--------------------------------------------------P-----------------------------#
# NETMRI-35732
$LOAD_PATH.insert(0, '/usr/lib/ruby/vendor_ruby/railties/lib/')

require 'rails_lib'
require 'net/http'
require 'json'
require 'util.rb'
require 'rubygems'
require 'etc'
require 'logger'
require 'lumberjack'
require 'lumberjack_syslog_device'
require 'lumberjack_multidevice.rb'
require 'digest/md5'
require 'fileutils'
require 'xml_settings/parse'
require 'active_support'
rails_dir = "/tools/skipjack/app/rails"
%w(app/metal app/models app/controllers app/helpers lib vendor).each { |subdir|
  ActiveSupport::Dependencies.autoload_paths << File.join(rails_dir, subdir)
}

require 'util'
Util.rails_init(logger: nil, test_mode: nil, dbconfig: {pool: 7})
require 'models/acl_user'
require 'models/virtual_network_member'
require 'models/virtual_network'
require 'models/virtual_network_member_assignment'

# general
BIN_PATH                = "/infoblox/netmri/bin/"
AUTOMATION_GRID_NODE    = "/infoblox/var/vnode_oid.txt"
PROBE_REGISTRY_SCRIPT   = "/infoblox/netmri/bin/populateUnitRegistry.pl"
MONITOR_PERIOD_DILIGENT = 60
MONITOR_PERIOD_NORMAL   = 600
CONFIG_SYNCED_FLAG      = "/tmpfs/discovery_conf_synced"

# config
CACHE_BASE              = "/dev/shm/config/"
CACHE_LOCATION          = "#{CACHE_BASE}cache/"
SCHEMA_LOCATION         = "/infoblox/netmri/db/system/config/"
SUPPORTED_SCHEMA = ["DiscoverySettings.xsd",
                    "NetworkAutomationGridMemberSettings.xsd",
                    "NetworkAutomationGridMemberSettings-1.1.xsd",
                    "NetworkAutomationGridMemberSettings-1.2.xsd"]
# data sync
DATASYNC_META           = "data_sync.meta"

# dsb
DSB_SYNC_LOCATION       = "/var/local/netmri/system/dsb_sync"
DSB_IMPORT_WAIT_TIMEOUT = 900
DSB_REMOVE_WAIT_TIMEOUT = 900
DSB_WAIT_INTERVAL       = 2
DSB_SYNC_ERROR_LOG      = "#{DSB_SYNC_LOCATION}/dsb_sync.meta"

#Vrf
VRF_SYNC_ERROR_LOG      = "/infoblox/netmri/logs/vrf_sync.meta"

CONSOLIDATOR            = '/tools/skipjack/app/WEB-INF/transaction/netmri/maintenance/wrapConsolidate.sh'

# acl / authenticated api calls
MASTER_HOSTNAME = 'oc-vpn'

class Monitor
  
  attr_accessor :api_host, :api_port, :api_user, :api_token
  
  #----------------------------------------------------------------#
  #  Begin monitoring the queue
  #----------------------------------------------------------------#
  def start()
    log('info', "Starting settings daemon in netmri mode: #{@netmri_mode}")
    return unless is_valid_mode?
    @last_dsb_sync = Time.now
    @last_vrf_sync = Time.now
    
    while true do
      begin
        unless @is_automation_grid
            # use the admin user account to make authenticated api calls
            admin_user = AclUser.with_decode.find('admin')
            @api_user  = admin_user.UserName    
            @api_token = admin_user.Password
        end
        
	sync_probes()
        
        query_settings()
        
        sync_settings()
   
	# TODO: It makes sense to have this call inside/as part of query_settings()
	# and then set the timezones via sync_settings() but until we convert the
	# NetworkAutomation side to use xml-based configs we call it here as an
	# additional step. 
	check_time_zones()
   
        @settings_check_count += 1
        sleep MONITOR_PERIOD_DILIGENT
     rescue => e
        # This is a catch all resuce. It is a VERY BAD THING if we are
        # here. It means something failed and collector sync was not
        # completed. This can lead to issues like duplicate DeviceIDs.
        #
        # Log backtrace to allow better troubleshooting
        log('error', "Failed to process settings monitor due to #{$!.message}:\n\t" + e.backtrace.join("\n\t"))
        sleep MONITOR_PERIOD_DILIGENT
      end
    end
  end
  
  
  #----------------------------------------------------------------#
  # sync current grid member id per every grid member and update 
  # probe registry if running in master.
  #----------------------------------------------------------------#
  def sync_probes
    return unless @is_automation_grid
    
    # get current grid member id (unit id)
    update_my_grid_member_id()
    
    return if @netmri_mode != 'master' or @test_mode

    if File.executable?(PROBE_REGISTRY_SCRIPT)
      `#{PROBE_REGISTRY_SCRIPT}`
      trace('sync_probes', "Updating probe registry")
    else
      log('error', "populateUnitRegistry script does not have executable permission.")
    end
  end
  
  #----------------------------------------------------------------#
  # Sync settings for all grid members
  #----------------------------------------------------------------#
  def sync_settings
    # no need to process further if settings_info is not successful
    return unless @settings_status[:query] 
    
    # pauses 10 min if all settings are in sync
    return if pause()

    # process config update on all members
    process_config()
    
    # process dsb update on probes
    begin
      process_dsb()
    rescue
      log('error', "Failed to proces dsb sync due to #{$!.message}")
    end

    #process vrf update in probes
    process_vrf()

    # process data sync on probes
    process_data_sync()

    if isGoodSetting?
      @monitor_timer_last_good_settings_check = Time.now.to_i
      #sync with NIOS processes
      FileUtils.touch CONFIG_SYNCED_FLAG if @is_automation_grid
    end
  end
  
  #----------------------------------------------------------------#
  # Query settings for each grid member including master, probes
  #----------------------------------------------------------------#
  def query_settings
    # initialize
    resetSettingStatus()
    @grid_members                         = {}
    @my_setting_info                      = {}
    @my_candidate_config_id               = ''
    @my_running_config_id                 = ''
    @my_candidate_config_exists           = false
    @my_running_config_exists             = false
    @my_config_local_change_required      = false
    @my_config_remote_change_required     = false
    @my_dsb_list                          = []
    @config_meta                          = nil
    @data_sync_meta                       = nil
    @my_data_sync_remote_change_required  = false
    @my_sync_vrf_data_required            = false
    
    # on Netmri it's always 0
    my_grid_member_id           = @my_grid_member_id.to_s.to_sym
    
    # get local member information
    begin
      #AUGUSTA-2325; using internal post_form
      response = self.post_form("http://#{@api_host}:#{@api_port}/api/2.8/settings_info",
        {},
				@api_user,
				@api_token
				)
      response_body = JSON.parse(response.body, :symbolize_names => true)
      # update grid members and local config ids
      @grid_members           = response_body[:grid_members]
      @my_setting_info        = @grid_members[my_grid_member_id]
      @my_candidate_config_id = @my_setting_info[:candidate_config]
      @my_running_config_id   = @my_setting_info[:running_config]
      @my_dsb_list            = @my_setting_info[:dsb_list]
    rescue
      @settings_status[:query] = false
      log('error', "Failed to query settings info on #{@netmri_mode} due to #{$!.message}")
    end
    
    trace('query_settings', "local grid member on #{@netmri_mode}: #{JSON.pretty_generate(@grid_members).force_encoding("UTF-8")}") if @netmri_mode == 'slave'
    if @grid_members.nil? || @grid_members.empty?
      @settings_status[:query] = false
      log('error', "Failed to query settings info on #{@netmri_mode} because @grid_members is empty")
      return
    end
    
    config_change_status()
    
    # futher processing is only for master
    return if @netmri_mode != 'master'

    #check if we need vrf_sync
    #we do not check on each member, we check if some changes exist since the last update,
    # else in 10 minutes we will anyway push it
    # change will be pushed anyway
    @my_sync_vrf_data_required = need_vrf_sync

    # NIOS-73482 remove offline members
    offline_members = @grid_members.select {|k,v| v[:status] == 'offline'}
    log('warn', "Members #{offline_members.keys.join(', ')} are offline, skipping") unless offline_members.empty?
    @grid_members.select! {|k,v| v[:status] != 'offline'}
    # update
    @grid_members.each do |unit_id, member|
      begin
        next if unit_id == my_grid_member_id        # skip master member
        #AUGUSTA-2325; using internal post_form
        response = self.post_form("http://#{member[:unit_ip]}:#{@api_port}/api/2.8/settings_info",
                                  {},
                                  @api_user,
                                  @api_token
                                 )
        response_body = JSON.parse(response.body, :symbolize_names => true)
        if response_body.nil? || response_body[:grid_members].nil?
          log('warn', "No response to query settings info on probe #{member[:unit_ip]}")
          next
        end
        if response_body[:grid_members][unit_id].nil?
          if response_body[:grid_members].keys.length == 1
            # this happens everytime on Netmri, deal with it
            # NETMRI-33134: unit_id is missing in response, but there's only 1 choice
            probe = response_body[:grid_members].values.first
          else
            log('warn', "unit_id #{unit_id} is missing in response: #{response_body[:grid_members]}, skipping member")
            next
          end
        else
          probe = response_body[:grid_members][unit_id]
        end
        #trace('query_settings', "probe member start: #{response_body}")
        #trace('query_settings', "probe member update: #{probe}")
        
        member[:connectivity]        = true
        member[:running_config]      = probe[:running_config]
        member[:candidate_config]    = probe[:candidate_config]
        member[:dsb_list]            = probe[:dsb_list]
        member[:device_count]        = probe[:device_count]
        member[:time_zone]           = probe[:time_zone]
        
        # these flags detect RMA case
        @my_config_remote_change_required = true    if @my_running_config_exists &&
                                                       probe[:running_config] != @my_running_config_id
        @my_data_sync_remote_change_required = true if probe[:device_count] == 0
      rescue => e
        member[:connectivity] = false
        log('error', "Failed to query settings info on probe #{member[:unit_ip]} due to #{$!.message}\n" + e.backtrace.join("\n"))
      end
    end
    
    trace('query_settings: grid_members', JSON.pretty_generate(@grid_members).force_encoding("UTF-8"))
    
  end
  
  #----------------------------------------------------------------#
  # Process config
  #
  #  1. load the candidate meta & update configs
  #  2. apply meta and make it to current
  #  3. probe config if master
  #----------------------------------------------------------------#
  def process_config
    # process this only in automation grid
    return unless @is_automation_grid

    begin      
      # local changes if candidate if found
      if @my_config_local_change_required
        log('info', "Config change detected locally")
        
        # update config stack to meta 
        update_meta()
        
        # apply meta
        apply_meta()
      else
        trace('process_config', "No local change is needed")
      end

      # remove changes for master
      probe_config() if @my_config_remote_change_required
    rescue
      log('error', "Failed to process config due to #{$!.message}")
    end
  end


  def need_vrf_sync
    return false unless @netmri_mode == 'master' && @is_automation_grid
    @last_vrf_sync = Time.now - (24*3600) if @last_vrf_sync.nil?
    check_date = @last_vrf_sync.strftime("%Y-%m-%d %H:%M:%S")
    list = VirtualNetworkMemberAssignment.find(:all, :conditions => ["VirtualNetworkMember.VirtualNetworkMemberArtificialInd = 0 and Timestamp >= ?", check_date],:include => [ :virtual_network_member ], :limit => 1)
    log('info', "Vrf need update: #{!list.empty?}")
    return list.present?
  end

  def process_vrf()
    return unless @netmri_mode == 'master' && @is_automation_grid
    failures = []
    my_grid_member_id = @my_grid_member_id.to_s.to_sym
    start_time = Time.now
    log('info', "Transferring vrfs")
    @grid_members.each do |unit_id, member|

        next if unit_id == my_grid_member_id        # skip myself (member)
        #transform symbol to string to please the sql request
        dataSourceID = unit_id.to_s

        # assign VRF network by network to the unit
        vn_for_unassigned=1
        VirtualNetwork.active_virtual_networks.each do | vn |
          begin
            vrf_assigned = VirtualNetworkMemberAssignment.find(:all, :conditions => ["vrfAssignment.VirtualNetworkID = ? and VirtualNetworkMember.DataSourceID = ? and VirtualNetworkMember.VirtualNetworkMemberArtificialInd = 0", vn.VirtualNetworkID, dataSourceID],:include => [ :virtual_network_member ])
            #send
            log('info', "Transferring assigned vrf from network(#{vn.VirtualNetworkID}) to Unit:#{unit_id} IP:#{member[:unit_ip]} size=#{vrf_assigned.size}")
            unless vrf_assigned.empty?
              failures << self.vrf_call_sync(unit_id, member, 'assign_members', vn.VirtualNetworkID, vrf_assigned)
            end
            vn_for_unassigned = vn
          end
        end

        #unassigned VRF network by network to the unit
        vrf_unassigned = VirtualNetworkMemberAssignment.find(:all, :conditions => ["VirtualNetworkMember.DataSourceID = ? and vrfAssignment.VirtualNetworkID = 0 and VirtualNetworkMember.VirtualNetworkMemberArtificialInd = 0", dataSourceID],:include => [ :virtual_network_member ])
        log('info', "Transferring unassigned vrf to Unit:#{unit_id} IP:#{member[:unit_ip]} size=#{vrf_unassigned.size}")
        unless vrf_unassigned.empty?
          failures << self.vrf_call_sync(unit_id, member, 'unassign_members', vn_for_unassigned.VirtualNetworkID, vrf_unassigned)
        end
        failures.reject!(&:empty?)

    end

    time_spent = Time.now - start_time

    @last_vrf_sync = Time.now - time_spent if failures.empty?

    if failures.present?
      @settings_status[:vrf] = false
      failures.flatten!
      save_vrf_sync_failures(failures)
      log('error', "Failed to sync vrf to probe(s):  #{failures.join("\n")}")
    end

    log('info', "Transferred vrfs")
  end

    #----------------------------------------------------------------#
    # processes vrf's to add and returns an array of failure hashes
    #----------------------------------------------------------------#
    def vrf_call_sync(unit_id, probe, action, vn_id, vrfs)
      ip_address = probe[:unit_ip]
      vrf_ids = vrfs.collect(&:VirtualNetworkMemberID)
      uri = "http://#{ip_address}:#{@api_port}/api/3/virtual_networks/#{action}"

      log("info", "@@@ #{uri} VNID:#{vn_id} Assign: #{vrf_ids}")
      failures = []
      begin
        response = self.post_form(uri, {:id => vn_id, :VirtualNetworkMemberID => vrf_ids.join(',')}, @api_user, @api_token)
        response_body = JSON.parse(response.body, :symbolize_names => true)
        if response_body[:status] == 0
          if response_body[:warning]
            failures << {:unit_id => unit_id, :unit_ip => probe[:unit_ip], :action => action,
                         :message => response_body[:message], :timestamp => Time.now.getutc}
          end
        end
            param_read = response_body[:read]
      rescue
        msg = "Error transferring #{action} #{$!.message}"
        log('error', msg)
        failures << {:unit_id => unit_id, :unit_ip => probe[:unit_ip], :action => action,
                     :message => $!.message, :timestamp => Time.now.getutc}
      end
      failures
    end

  #----------------------------------------------------------------#
  # Log vrf sync failures
  #----------------------------------------------------------------#
  def save_vrf_sync_failures(failures)
    File.open(VRF_SYNC_ERROR_LOG, 'w') do |f2|
      f2.puts JSON.pretty_generate(failures, {:max_nesting => false}).force_encoding("UTF-8")
    end
  end

  #----------------------------------------------------------------#
  # Process DSB bundles
  # Executed only in master.  
  #----------------------------------------------------------------#
  def process_dsb()
    return if @netmri_mode != 'master'
    failures = []
    
    master_dsb_list = filter_dsb_list(@my_setting_info[:dsb_list])
    trace('process_dsb: master_dsb_list', master_dsb_list)
    
    master_md5      = generate_md5(master_dsb_list)                
    trace('process_dsb: master_md5', master_md5)
    
    my_grid_member_id = @my_grid_member_id.to_s.to_sym

    # find the latest timestamp of the DSB files
    @last_dsb_sync = Time.now - (24*3600) if @last_dsb_sync.nil?
    log('debug', "DSB Sync last timesamp #{@last_dsb_sync}")
    latest_file_ts = @last_dsb_sync
    dsb_timestamps = {}
      master_dsb_list.each do |dsb|
	dsb_name = dsb[:name]
	dsb_files = Dir["#{DSB_SYNC_LOCATION}/#{dsb_name}.*"]
	dsb_files.each do |f|
	  ts = File.ctime(f)
	  latest_file_ts = ts if ts > latest_file_ts
	  dsb_timestamps[dsb_name] = ts
	end
    end
    log('debug', "DSB Sync latest file timestamp #{latest_file_ts}")
    
    @grid_members.each do |unit_id, member|
      begin
        next if unit_id == my_grid_member_id       # skip master member
        log('debug', "DSB Sync checking member #{member[:unit_ip]}")
        
        member_dsb_list = filter_dsb_list(member[:dsb_list])
        trace('process_dsb: member_dsb_list', member_dsb_list)
        
        member_md5      = generate_md5(member_dsb_list)
        trace('process_dsb: member_md5', member_md5)
  
        log('debug', "DSB Sync, Checking for changes: #{member_md5} != #{master_md5} || #{@last_dsb_sync} < #{latest_file_ts}")
  
        if member_md5 != master_md5 || @last_dsb_sync < latest_file_ts
	  log('debug', "DSB change detected")
          unless member[:connectivity]
            @settings_status[:dsb] = false
            log('error', "Failed to connect probe #{member[:unit_ip]} for dsb sync")
            failures << {:unit_id     => unit_id,
                         :unit_ip     => member[:unit_ip],
                         :dsb_name    => '',
                         :message     => "no connectivity to member...skip",
                         :timestamp   => Time.now.getutc}
            next
          end
          
          add_list    = master_dsb_list.reject{|m|
	    # reject if it already exists and if the file timestamp is less than the last sync
	    member_dsb_list.include?(m) && !dsb_timestamps[m[:name]].nil? && (dsb_timestamps[m[:name]] < @last_dsb_sync)
	  }
    
          delete_list = member_dsb_list.reject{|s| master_dsb_list.include? s}

          failures << dsb_add_sync(unit_id, member, add_list)
          failures << dsb_delete_sync(unit_id, member, delete_list)
          failures.reject!{ |arr| arr.empty?}      
        end
      rescue
        @settings_status[:dsb] = false
        log('error', "Failed to sync probe dsb to #{member[:unit_ip]} due to #{$!.message}")
      end        
    end
    @last_dsb_sync = Time.now if failures.empty?
    
    if !failures.empty?
      @settings_status[:dsb] = false
      failures.flatten!
      save_dsb_sync_failures(failures)
      log('error', "Failed to sync dsb to probe(s): " + failures.join("\n"))
    end
  end
  
  #----------------------------------------------------------------#
  # Process data sync
  # Executed only in master. 
  #
  # data sync status: pending, failed, complete
  #----------------------------------------------------------------#
  def process_data_sync()
    return if @netmri_mode != 'master'
    @data_sync_meta = {} 
    @data_sync_meta["ProbeStatus"] = {}
    
    # check each probe for data sync 
    my_grid_member_id = @my_grid_member_id.to_s.to_sym
    @grid_members.each do |unit_id, probe|
      begin
        next if unit_id == my_grid_member_id        # skip myself (member)
        unless probe[:connectivity]
          @settings_status[:data_sync] = false
          log('error', "Failed to connect probe #{probe[:unit_ip]} for config sync")
          @data_sync_meta["ProbeStatus"][unit_id] = { "UnitIP" => probe[:unit_ip], "Status" => "failed", "Description" => "Failed to connect probe" }
          next
        end
        
        trace("process_data_sync", "ip: #{probe[:unit_ip]}, oc device: #{probe[:oc_device_count]}, probe device: #{probe[:device_count]}")        

        # process data sync if netmri.Device is empty on the Collector
        if (probe[:device_count] == 0)
          response = syncProbeData(unit_id, probe[:unit_ip], probe[:network_id])
          trace("process_data_sync", "ip: #{probe[:unit_ip]}, #{probe[:network_id]}, response: #{JSON.pretty_generate(response).force_encoding("UTF-8")}")
          
          probe_status        = 'complete'
          probe_message       = ''
          probe_archive_file  = response[:archive_file]
          if response[:success]
              if File.exists?(probe_archive_file)

                url = "http://#{probe[:unit_ip]}:#{@api_port}/api/2.8/collector_data_sync/import_data"
                curl_cmd = ['curl', '-F', "media=\@#{probe_archive_file}"]
                if @api_user && @api_token
                  curl_cmd << '--user'
                  curl_cmd << "#{@api_user}:#{@api_token}"
                end
                curl_cmd << url
                # using curl due to lack of options for posting file uploads
                response, err, status = Open3.capture3(*curl_cmd)
                if (status.success?)
                  response.force_encoding("UTF-8")
                  response_body = JSON.parse(response, :symbolize_names => true)
                  log('info', "Transferred #{probe_archive_file} to #{probe[:unit_ip]}")
                else
                  @settings_status[:data_sync] = false
                  probe_status = 'failed'
                  probe_message = err
                  log('error', "Failed to sync probe data via curl to #{probe[:unit_ip]}: status=#{status}: #{probe_message}")
                end
                probe_archive_path = File.dirname(probe_archive_file)
                FileUtils.rm_rf Dir.glob(probe_archive_path)
                trace('process_data_sync', "#{probe_archive_path} has been created")
              end
          else
            @settings_status[:data_sync] = false
            probe_status = 'failed'
            probe_message = response[:message]
            log('error', "Failed to sync probe data to #{probe[:unit_ip]}: #{probe_message}")
          end
          
          @data_sync_meta["ProbeStatus"][unit_id] = { "UnitIP" => probe[:unit_ip], "Status" => probe_status, "Description" => probe_message }
        else

          trace('process_data_sync', "#{probe[:unit_ip]}: no data sync is needed")
          @data_sync_meta["ProbeStatus"][unit_id] = { "UnitIP" => probe[:unit_ip], "Status" => "complete", "Description" => "No need for data sync" }
          next
        end
      rescue => e
        @settings_status[:data_sync] = false
        log('error', "Failed to sync probe data to #{probe[:unit_ip]} due to #{$!.message}:\n\t" + e.backtrace.join("\n\t"))
      end
    end # end of probe loop
    
    saveDataSyncMeta()
  end

  
  #----------------------------------------------------------------#
  # Check configs and determine actions
  #
  # All possible cases are
  # 1. both candidate and running could be empty: nothing to do
  # 2. only candidate exists: config is imported for the first time, process it
  # 3. only running exists: no local change but remote change may be needed
  # 4. both configs exist: both local and remote changes may be needed
  #
  # All remote changes should be done by master only
  #----------------------------------------------------------------#  
  def config_change_status
      @my_candidate_config_exists = !(@my_candidate_config_id !~ /\S/)
      @my_running_config_exists   = !(@my_running_config_id !~ /\S/)
      
      @my_config_local_change_required  = @my_candidate_config_exists
      @my_config_local_change_required  = false if @my_candidate_config_exists && 
                                                   @my_running_config_exists &&
                                                   @my_candidate_config_id == @my_running_config_id
      @my_config_remote_change_required = @netmri_mode == 'master' && 
                                          @my_config_local_change_required
      
      trace("config_change_status", 
        "candidate? #{@my_candidate_config_exists}, running? #{@my_running_config_exists}, " +
        "local? #{@my_config_local_change_required}, remote? #{@my_config_remote_change_required}")
  end
  
  #----------------------------------------------------------------#
  # Updates meta with complete change sets for all member.
  #
  # parse status: pending, invalid, parsing, updating, complete
  #----------------------------------------------------------------#
  def update_meta
    getConfigMeta(@my_candidate_config_id)
    log('info', "Updating meta for #{@my_candidate_config_id}, status: #{@config_meta["messages"]["ParseStatus"]}")
    
    # status is complete, no need to process
    return if @config_meta["messages"]["ParseStatus"] == 'complete'
        
    schema = @config_meta["messages"]["Schema"]
    
    log('debug', "Determining schema version for #{schema}")
    if SUPPORTED_SCHEMA.include?(schema)
      log('debug', "Schema is ok")
    else
      @settings_status[:config] = false
      log('info', "Invalid schema failed to apply configuration")
      setConfigStatus('parse', 'invalid', "Configuration #{@my_candidate_config_id} schema not yet supported")
      return
    end
    
    status = true
    begin
      log('debug', "Running parser with params:\n\t*** Config ID:#{@my_candidate_config_id} NetMRI Mode:#{@netmri_mode} ***\n")
      XmlSettings::Parse.new(@logger).parse(@my_candidate_config_id, @netmri_mode)
      trace('Parser', "status: ok")
    rescue => e
      status = false
      trace('Parser', "status: error, message: #{e.message}")
      log('error', "Error on parsing #{@my_candidate_config_id}: #{e.message}\n" + e.backtrace.join("\n"))
    end
    
    # needs to reload since parser has updated meta
    getConfigMeta(@my_candidate_config_id)
    @settings_status[:config] = status
  end
  
  #----------------------------------------------------------------#
  # Applies meta locally 
  # 
  # apply status: 
  #    global: pending, failed, complete
  #----------------------------------------------------------------#
  def apply_meta
    log('info', "Applying meta for #{@my_candidate_config_id}, status: #{@config_meta["messages"]["ApplyStatus"]}")
    return if @config_meta["messages"]["ApplyStatus"] == "complete"
    
    @config_meta["failed"] = []
    
    log('info', "Applying #{@my_candidate_config_id} to #{@netmri_mode}...")
   
    result = applyMeta('localhost', @config_meta["Change Stack"])
    @config_meta["Change Stack"] = result unless result[:update].nil?
    @config_meta["failed"].concat(@config_meta["Change Stack"]["failed"]) unless @config_meta["Change Stack"]["failed"].nil?      
    
    if @config_meta["failed"].size > 0
      @config_meta["messages"]["ApplyStatus"]      = "partial"
      @config_meta["messages"]["ApplyDescription"] = "Completed with some failures"
    else
      @config_meta["messages"]["ApplyStatus"]      = "complete"
      @config_meta["messages"]["ApplyDescription"] = "Completed applying configuration"
    end
    
    saveConfigMeta(@my_candidate_config_id)
    
    #make the file the current cache
    skip_symlink_creation = false
    if @my_running_config_exists
      # do not delete symlink and config files if the current config is applied
      if @my_running_config_id == @my_candidate_config_id
        skip_symlink_creation = true
      else
        File.delete("#{CACHE_LOCATION}current")
        File.delete("#{CACHE_LOCATION}#{@my_running_config_id}.meta")
        File.delete("#{CACHE_LOCATION}#{@my_running_config_id}.xml")        
      end
    end
    
    # now candidate config is running config, no more candidate
    unless skip_symlink_creation
      File.delete("#{CACHE_LOCATION}current") if File.exists?("#{CACHE_LOCATION}current")
      File.symlink("#{CACHE_LOCATION}#{@my_candidate_config_id}.meta", "#{CACHE_LOCATION}current")
    end
    @my_running_config_id       = @my_candidate_config_id
    @my_running_config_exists   = true
    @my_candidate_config_id     = ''
    @my_candidate_config_exists = false
    
    force_discovery_settings_update()
    sendNotifyRevisionMessage()
  end
  
  #----------------------------------------------------------------#
  # AUGUSTA-2325; Timeouts are creating havoc when applying configs
  #               in extremely large grid configurations.
  # To solve the problem, I've upped the timeout to 2000 seconds.
  # This however requires much more code than the primitive NetHHTP
  # post_form method, so we've implemented our own.
  #----------------------------------------------------------------#
  def post_form(url, data, user, token)
    @post_form_tries += 1
    http = nil
    uri = URI.parse(url)
    if @api_ssl_port && @post_form_tries.odd?
      http = Net::HTTP.new(uri.host, @api_ssl_port)
      http.use_ssl = true
      http.verify_mode = OpenSSL::SSL::VERIFY_NONE
    else
      http = Net::HTTP.new(uri.host, uri.port)
    end

    http.read_timeout = 2000
    request = Net::HTTP::Post.new(uri.request_uri)
    request.set_form_data(data)
    if user && token # For NetMRI/NetworkAutomation we need to auth all requests.
     request.basic_auth user, token
    end
    response = http.request(request)
    response.body.force_encoding("UTF-8")

    if response.code == "200"
      @post_form_tries = @post_form_tries.odd? ? 0 : 1
    end 

    return response
  end

  #----------------------------------------------------------------#
  # merge common mods with specific for this
  # host and execute apply
  #----------------------------------------------------------------#
  def applyMeta(host, change_stack)
    msg = ""
    cs = {
      'ignore' => [],
      'create' => [],
      'update' => [],
      'remove' => [],
      'failed' => []
    }
    
    # copy all mods to new change stack
    cs.each_key do |action|
      change_stack[action].each { |mod| cs[action] << mod } if !change_stack.nil? and !change_stack[action].nil?
    end
    
    mods = cs.to_json
    #trace('applyMeta: mods', mods)
    
    begin
      #AUGUSTA-2325; using internal post_form
      response = self.post_form("http://#{@api_host}:#{@api_port}/api/2.8/settings_apply",
                                {'config_id' => @my_candidate_config_id, 'mods' => mods},
				@api_user,
				@api_token
			       );
      JSON.parse(response.body, :symbolize_names => true)
    rescue
      @settings_status[:config] = false
      msg = "Error on applying meta for #{host} due to #{$!.message}"
      log('error', msg)      
    end
  end
  
  #----------------------------------------------------------------#
  # Transfers corresponding probe change set to each probe
  # Executed only in master.
  #
  # transfer status: 
  #    probe:  pending, failed, complete
  #----------------------------------------------------------------#
  def probe_config
    # for slave and stanalone, no need to do this. only master is responsible for transfering meta to collectors
    return if @netmri_mode != 'master'
    
    log('info', "Probing config for #{@my_running_config_id}")
    
    getConfigMeta(@my_running_config_id)
    
    @config_meta["messages"]["ProbeStatus"] = {}
    
    # sync each probe's running config to be master's running config
    my_grid_member_id = @my_grid_member_id.to_s.to_sym
    @grid_members.each do |unit_id, probe|
      begin
        next if unit_id == my_grid_member_id        # skip myself (member)
        
        unless probe[:connectivity]
          @settings_status[:config] = false
          log('error', "Failed to connect probe #{probe[:unit_ip]} for config sync")
          @config_meta["messages"]["ProbeStatus"][unit_id] = { "UnitIP" => probe[:unit_ip], "Status" => "failed", "Description" => "Failed to connect probe" }          
          next
        end
        
        trace("probe_config", "ip: #{probe[:unit_ip]}, master: #{@my_running_config_id}, probe: #{probe[:running_config]}")        
        
        # no need to process if probe is up to date
        if probe[:running_config] == @my_running_config_id
          trace('probe_config', "#{probe[:unit_ip]}: config is in sync already")
          @config_meta["messages"]["ProbeStatus"][unit_id] = { "UnitIP" => probe[:unit_ip], "Status" => "complete", "Description" => "" }
          next
        end
        
        # sync probe
        response = postConfig(probe[:unit_ip])
        trace("probe_config", "ip: #{probe[:unit_ip]}, response: #{JSON.pretty_generate(response).force_encoding("UTF-8")}")
        
        probe_status      = 'complete'
        probe_message     = ''
        unless response[:success]
          @settings_status[:config] = false
          probe_status = 'failed'
          probe_message = response[:message]
          log('error', "Failed to sync probe config on #{probe[:unit_ip]}: #{probe_message}")
        end
        
        @config_meta["messages"]["ProbeStatus"][unit_id] = { "UnitIP" => probe[:unit_ip], "Status" => probe_status, "Description" => probe_message }
      rescue
        @settings_status[:config] = false
        log('error', "Failed to sync probe config on #{probe[:unit_ip]} due to #{$!.message}")
      end
    end
    
    trace('probe_config', "probe results: " + JSON.pretty_generate(@config_meta["messages"]["ProbeStatus"]).force_encoding("UTF-8"))
    saveConfigMeta(@my_running_config_id)
  end

  #----------------------------------------------------------------#
  # Posts probe meta to each probe
  #----------------------------------------------------------------#
  def postConfig(probe_ip)
    msg = ''
    begin
      xml_content = File.read("#{CACHE_LOCATION}#{@my_running_config_id}.xml")
      #AUGUSTA-2325; using internal post_form
      response = self.post_form("http://#{probe_ip}:#{@api_port}/api/2.8/settings_import",
                                {'config_id' => @my_running_config_id, 'xml_content' => xml_content},
				@api_user,
				@api_token
			       )
      response_body = JSON.parse(response.body, :symbolize_names => true)
      return {:success => response_body[:status], :message => response_body[:messages]}
    rescue
      msg = "Error on transferring meta for #{probe_ip} due to #{$!.message}"
      log('error', msg)
    end
    {:success => false, :message => msg}
  end
  
  #----------------------------------------------------------------#
  # Log dsb sync failures
  #----------------------------------------------------------------#
  def save_dsb_sync_failures(failures)
    File.open(DSB_SYNC_ERROR_LOG, 'w') do |f2|
      f2.puts JSON.pretty_generate(failures, {:max_nesting => false}).force_encoding("UTF-8")
    end
  end

  #----------------------------------------------------------------#
  # processes dsb's to add and returns an array of failure hashes
  #----------------------------------------------------------------#
  def dsb_add_sync(unit_id, probe, adds)
    failures = []
    adds.each {|dsb|
      dsb_name = dsb[:name]
      log('info', "DSB Add Sync on #{dsb_name}")
      ip_address = probe[:unit_ip]
      dsb_files = Dir["#{DSB_SYNC_LOCATION}/#{dsb_name}.*"]
      if dsb_files.empty?
        failures << {:unit_id => unit_id, :unit_ip => probe[:unit_ip], :dsb_name => dsb[:name],
          :message => "dsb sync file for #{dsb_name} not found on master", :timestamp => Time.now.getutc}
        next
      end
      dsb_file = dsb_files[0]

      import_uri = "http://#{ip_address}:#{@api_port}/api/2.8/device_support_bundles/import?overwrite=true"
      curl_cmd = ['curl', '-F', "file=\@#{dsb_file}"]
      if @api_user && @api_token
        curl_cmd << '--user'
        curl_cmd << "#{@api_user}:#{@api_token}"
      end
      curl_cmd << import_uri

      # using curl due to lack of options for posting file uploads
      response, err, status = Open3.capture3(*curl_cmd)
      unless status.success?
        if err.match('read function returned funny value')
          err = 'unable to access file, check permissions'
        end
        failures << {:unit_id => unit_id, :unit_ip => probe[:unit_ip], :dsb_name => dsb[:name],
          :message => "failed to upload dsb file: #{err}", :timestamp => Time.now.getutc}
        next
      end

      response.force_encoding("UTF-8")
      response_body = JSON.parse(response, :symbolize_names => true)
      
      if response_body[:id]
        wait_interval = DSB_WAIT_INTERVAL
        wait_timeout = DSB_IMPORT_WAIT_TIMEOUT
        wait_flag = 1
        param_read = 0
        param_id = response_body[:id]
        
        # need to go into a loop to keep checking output from the import
        # this can take up to a few minutes
        while wait_flag == 1 && wait_timeout > 0 
          sleep(wait_interval);
          wait_timeout -= wait_interval;
          begin
            #AUGUSTA-2325; using internal post_form
            response = self.post_form(import_uri, {'id' => param_id, 'read' => param_read}, @api_user, @api_token)
            response_body = JSON.parse(response.body, :symbolize_names => true)
         
            if response_body[:status] == 0
              wait_flag = 0
              if response_body[:warning]
                failures << {:unit_id => unit_id, :unit_ip => probe[:unit_ip], :dsb_name => dsb[:name], 
                  :message => response_body[:message], :timestamp => Time.now.getutc}
              end
            end
            param_read = response_body[:read]    
          rescue
            wait_flag = 0
            msg = "Error checking status on dsb import #{$!.message}"
            log('error', msg)
            failures << {:unit_id => unit_id, :unit_ip => probe[:unit_ip], :dsb_name => dsb[:name], 
              :message => $!.message, :timestamp => Time.now.getutc}            
          end       
        end 
        if wait_timeout <= 0
          failures << {:unit_id => unit_id, :unit_ip => probe[:unit_ip], :dsb_name => dsb[:name],
            :message => "import is taking more than #{DSB_IMPORT_WAIT_TIMEOUT} seconds", :timestamp => Time.now.getutc}
        end
      else
        failures << {:unit_id => unit_id, :unit_ip => probe[:unit_ip], :dsb_name => dsb[:name],
          :message => response_body[:message], :timestamp => Time.now.getutc} 
      end
    }
    failures
  end
  
  #------------------------------------------------------------------#
  # handles deletes on collectors
  #------------------------------------------------------------------#
  def dsb_delete_sync(unit_id, probe, deletes)
    failures = []
    ip_address = probe[:unit_ip]
    deletes.each {|dsb|  
      begin
        #AUGUSTA-2325; using internal post_form
        response = self.post_form("http://#{ip_address}:#{@api_port}/api/2.8/device_support_bundles/delete",
                                  {'dsb_name' => dsb[:name]},
				  @api_user,
				  @api_token
				 )
        response_body = JSON.parse(response.body, :symbolize_names => true)     
        if response_body[:id]
          wait_interval = DSB_WAIT_INTERVAL
          wait_timeout = DSB_REMOVE_WAIT_TIMEOUT
          wait_flag = 1
          param_read = 0
          param_id = response_body[:id]
          # need to go into a loop to keep checking output from the delete
          while wait_flag == 1 && wait_timeout > 0
            sleep(wait_interval);
            wait_timeout -= wait_interval;
            
            # need to exit while loop immediately if exception is thrown
            begin
              #AUGUSTA-2325; using internal post_form
              response = self.post_form("http://#{ip_address}:#{@api_port}/api/2.8/device_support_bundles/delete",
                                        {'id' => param_id, 'read' => param_read},
					@api_user,
					@api_token
				       )
              response_body = JSON.parse(response.body, :symbolize_names => true)
              if response_body[:status] == 0
                wait_flag = 0
                if response_body[:warning]
                  failures << {:unit_id => unit_id, :unit_ip => probe[:unit_ip], :dsb_name => dsb[:name],
                    :message => response_body[:message], :timestamp => Time.now.getutc}
                end
              end
              param_read = response_body[:read]
            rescue
              wait_flag = 0
              msg = "Error on deleting dsb #{$!.message}"
              log('error', msg)
              failures << {:unit_id => unit_id, :unit_ip => probe[:unit_ip], :dsb_name => dsb[:name], :message => $!.message,
                  :timestamp => Time.now.getutc}              
            end
          end 
          if wait_timeout <= 0
            failures << {:unit_id => unit_id, :unit_ip => probe[:unit_ip], :dsb_name => dsb[:name],
              :message => "delete is taking more than #{DSB_REMOVE_WAIT_TIMEOUT} seconds", :timestamp => Time.now.getutc}
          end          
        else
          failures << {:unit_id => unit_id, :unit_ip => probe[:unit_ip], :dsb_name => dsb[:name], :message => response_body[:message],
            :timestamp => Time.now.getutc}
        end
      rescue
        msg = "Error on deleting dsb #{$!.message}"
        log('error', msg)
        failures << {:unit_id => unit_id, :unit_ip => probe[:unit_ip], :dsb_name => dsb[:name], :message => $!.message,
            :timestamp => Time.now.getutc}
      end
    }
    failures
  end
  
  #----------------------------------------------------------------#
  # filter out non-relevant key/values
  #----------------------------------------------------------------#
  def filter_dsb_list(dsb_list)
    filtered_dsb_list = []
    return filtered_dsb_list if dsb_list.nil? or dsb_list.empty?
    
    dsb_list.each do |dsb|
      filtered_dsb_list << dsb.select{|k,v| [:author, :name, :version, :status].include? k} if dsb[:status] == 'Installed'
    end
    filtered_dsb_list
  end
  
  #----------------------------------------------------------------#
  # Create md5-friendly string representation of an array of hashes
  #----------------------------------------------------------------#
  def generate_md5(dsb_list)
    return "" if dsb_list.empty?
    dsb_list.sort_by! { |dsb| dsb[:name] }
    md5_str = Digest::MD5.hexdigest(dsb_list.map { |dsb| dsb.sort.map {|k,v| "#{v}"}}.join(""))
    md5_str
  end
  
  #----------------------------------------------------------------#
  # Posts probe meta to each probe
  #----------------------------------------------------------------#
  def syncProbeData(probe_id, probe_ip, probe_network_id)
    msg = ''
    begin
      #AUGUSTA-2325; using internal post_form
      response = self.post_form("http://#{@api_host}:#{@api_port}/api/2.8/collector_data_sync/send_data_to_collector",
                                {'unit_id' => probe_id, 'unit_ip' => probe_ip, 'unit_network_id' => probe_network_id, 'port' => 82},
				@api_user,
				@api_token
			       )
      response_body = JSON.parse(response.body, :symbolize_names => true)
      trace('syncProbeData response', JSON.pretty_generate(response_body).force_encoding("UTF-8"))
      success = true
      message = ''
      if response_body[:archive_file] !~ /\S/
        success = false
        message = 'Failed to sync probe data; no archive file has been generated'
      end
      return {:success => success, :archive_file => response_body[:archive_file], :message => message}
    rescue => e
      msg = "Error on syncing data on #{probe_ip} due to #{$!.message}:\n\t" + e.backtrace.join("\n\t")
      log('error', msg)
    end
    {:success => false, :archive_file => '', :message => msg}
  end
  
  #----------------------------------------------------------------#
  # Executes Settings consolidator after applying new meta file
  #----------------------------------------------------------------#
  def force_discovery_settings_update()
    log('debug', "Executing Settings consolidator")
    system('chown netmri.users /infoblox/netmri/logs/*.log')
    system(CONSOLIDATOR + ' -type Settings SkipRecentEditCheck 1')
    if $?.success?
      log('debug',"Consolidator has finished without errors")
    else
      log('warn', "Consolidator exited with status #{$?.exitstatus}")
    end
  end

  #----------------------------------------------------------------#
  # Makes a connection to MQ and notifies
  # the world new configuration id 
  #
  # Key: local.configuration.applied.revision
  # Exchange: admin-inform
  #----------------------------------------------------------------#
  def sendNotifyRevisionMessage()
    log('info', "Informing other processes that new configuration id is '#{@config_meta["niosconfigid"]}'")
    begin
      NetMRI::MQ.new.channel.inform body:     @config_meta["niosconfigid"],
                                    exchange: 'admin-inform',
                                    key:      "local.configuration.applied.revision"
    rescue
      log('error', "Something went wrong sending notification revision message due to #{$!.message}")
    end
  end

  #----------------------------------------------------------------#
  # Returns either nil (bad config id) or a
  # hash with meta infomation on the
  # configuration id
  #----------------------------------------------------------------#
  def getConfigMeta(config_id)
    metafile = "#{CACHE_LOCATION}#{config_id}.meta"
    if File.exists?(metafile)      
      str =  IO.read("#{CACHE_LOCATION}#{config_id}.meta")
      str.force_encoding("UTF-8")
      @config_meta = JSON.parse(str)
    end
  end
    
  #----------------------------------------------------------------#
  # Save the config meta file
  #----------------------------------------------------------------#
  def saveConfigMeta(config_id)
    #save the changes and give them back to the caller
    cfgid = @config_meta["messages"]["Config Id"]
    File.open("#{CACHE_LOCATION}#{cfgid}.meta", File::RDWR|File::CREAT, 0644) do |f|
      f.flock(File::LOCK_EX)
      f.rewind
      f.write(JSON.pretty_generate(@config_meta, {:max_nesting => false}).force_encoding("UTF-8"))
      f.flush
      f.truncate(f.pos)
    end
  end

  #----------------------------------------------------------------#
  # Save the data sync meta file
  #----------------------------------------------------------------#
  def saveDataSyncMeta()
    #save the changes and give them back to the caller
    metafile = "#{CACHE_LOCATION}#{DATASYNC_META}"
    File.open(metafile, 'w') do |f2|  
      f2.puts JSON.pretty_generate(@data_sync_meta, {:max_nesting => false}).force_encoding("UTF-8")
    end
  end
  
  #----------------------------------------------------------------#
  # Set config status per each action
  #----------------------------------------------------------------#
  def setConfigStatus(type, status, description)
    case type
    when 'parse'
      @config_meta["messages"]["ParseDescription"] = description
      @config_meta["messages"]["ParseStatus"] = status 
       saveConfigMeta(@my_candidate_config_id)
    when 'apply'
      @config_meta["messages"]["ApplyDescription"] = description
      @config_meta["messages"]["ApplyStatus"] = status
      saveConfigMeta(@my_candidate_config_id)
    when 'probe'
      @config_meta["messages"]["TransferStatus"] = status
      saveConfigMeta(@my_running_config_id)
    end
  end  
   
  #----------------------------------------------------------------#
  # when in test mode, output to console 
  #----------------------------------------------------------------#
  def trace(name, msg)
    puts "#{name} => #{msg}" if @test_mode
  end
  
  #----------------------------------------------------------------#
  # encapsulates logging and adds console output
  #----------------------------------------------------------------#
  def log(log_type, log_msg)
    case log_type 
    when 'info'
      @logger.info { log_msg }
    when 'debug'
      @logger.debug { log_msg }
    when 'warn'
      @logger.warn { log_msg }
    when 'error'
      @logger.error { log_msg }
    when 'fatal'
      @logger.fatal { log_msg }
    end

    #puts "#{log_msg}" if @trace
  end
  
  #----------------------------------------------------------------#
  # Resets settings statues
  #----------------------------------------------------------------#
  def resetSettingStatus
    @settings_status[:query]      = true
    @settings_status[:config]     = true
    @settings_status[:dsb]        = true
    @settings_status[:vrf]        = true
    @settings_status[:data_sync]  = true
  end
  
  #----------------------------------------------------------------#
  # Check if all settings are in good condition
  #----------------------------------------------------------------#
  def isGoodSetting?
    return  @settings_status[:query] &&
              @settings_status[:config] &&
                @settings_status[:dsb] &&
                  @settings_status[:vrf] &&
                    @settings_status[:data_sync]
  end
  
  #----------------------------------------------------------------#
  # Pause some time if settings are in good condition
  #----------------------------------------------------------------#
  def pause
    # if settingsd ran for the first time, settings are not in sync, or
    # config change needs to be processed locally or remotely, do not pause
    if @settings_check_count == 0 ||
       !isGoodSetting? ||
       @my_config_local_change_required || 
       @my_config_remote_change_required ||
       @my_data_sync_remote_change_required ||
       @my_sync_vrf_data_required
      return false
    end
    
    timediff = Time.now.to_i - @monitor_timer_last_good_settings_check
    trace('pause', "checking for pause: #{timediff}")
    return false if timediff > MONITOR_PERIOD_NORMAL
    
    trace('pause', "pausing for #{MONITOR_PERIOD_NORMAL} >>>>>>>>>>")
    return true
  end
  
  #----------------------------------------------------------------#
  # if netmri mode is valid, return false; otherwise true.
  #----------------------------------------------------------------#  
  def is_valid_mode?
    if (@is_automation_grid) #NIOS supports all modes.  
      return ['standalone', 'master' ,'slave'].include?(@netmri_mode)
    else # NetMRI only supports OC-Collector configuration per NETMRI-21854.
      return ['master' ,'slave'].include?(@netmri_mode)
   end
  end
  
  #----------------------------------------------------------------#
  # get the current grid member id
  #----------------------------------------------------------------#  
  def update_my_grid_member_id
    @my_grid_member_id = `cat /infoblox/var/vnode_oid.txt`.gsub("\n",'').to_i
  end
  
  #----------------------------------------------------------------#
  # Class Initializer
  #----------------------------------------------------------------#
  def initialize(proc_name, detach, debug, test)
    @proc_name = proc_name

    @is_automation_grid = File.exists?(AUTOMATION_GRID_NODE)

    #/dev/shm/config/cache may not exist... make sure it does
    #This is also done in settingsd, and allows us to ignore
    #the chicken & egg issue of who came first
    dirname = File.dirname(CACHE_LOCATION)
    unless File.directory?(dirname)
      `/bin/mkdir -p #{CACHE_LOCATION}; /bin/chown -R netmri.mysql #{CACHE_BASE}`
    end
    
    #current may not exist, we can make a polite request to
    #get NIOS to resend it. 
    unless File.exists?("#{CACHE_LOCATION}current") || !@is_automation_grid
      `/infoblox/netmri/bin/sync_discovery_conf -s &`
    end
    
    if detach
      syslog_device = Lumberjack::SyslogDevice.new(options = {
        :options => Syslog::LOG_PID | Syslog::LOG_NDELAY,
        :facility => Syslog::LOG_USER} )
      file_device = Lumberjack::Device::LogFile.new(
        path = "/tools/skipjack/logs/#{@proc_name}.log"
      )
      # Use file logging for now
      device = Lumberjack::Device::MultiDevice.new({
        syslog_device => :notify,
        file_device => :debug
      })

      @logger = Lumberjack::Logger.new(device, options = {
        :progname => @proc_name, :flush_seconds => 5} )
      #stdin = open '/dev/null', 'r'
      #stdout = open '/dev/null', 'w'
      #stderr = open '/dev/null', 'w'
      #STDIN.reopen stdin
      #STDOUT.reopen stdout
      #STDERR.reopen stderr
    else
      @logger = Lumberjack::Logger.new(STDERR)
    end

    @logger.level = debug ? Lumberjack::Logger::DEBUG :
                            Lumberjack::Logger::INFO
    @test_mode = test

    # Defaults for NetworkInsight
    @api_host  = "localhost"
    @api_port  = "82"
    @api_ssl_port = nil
    @api_user  = nil
    @api_token = nil
    @post_form_tries = 0
    # If not NetworkInsight replace with NetworkAutomation overrides.
    unless @is_automation_grid
      @api_port = "80"
      @api_ssl_port = "443"
    end

    @netmri_mode = `/tools/skipjack/bin/GetNetMRIMode.pl`
    
    if @is_automation_grid
      #TODO: this fails on NetworkAutomation, but isnt needed? Need to handle gracefully.
      @my_grid_member_id = `cat /infoblox/var/vnode_oid.txt`.gsub("\n",'').to_i
    else
      @my_grid_member_id = 0
    end
 
    @grid_members = nil         # contains all grid mebmer's setting info
    @my_setting_info = nil      # contains my (local) setting info only
 
    # config related
    @config_meta                = nil
    @my_candidate_config_id     = ''
    @my_running_config_id       = ''
    @my_candidate_config_exists = false
    @my_running_config_exists   = false
    
    # dsb related
    @my_dsb_list                = []
    
    # data sync related
    @data_sync_meta             = nil
    @my_data_sync_remote_change_required = false

    #vrf
    @my_sync_vrf_data_required = false
    
    # all settings status check
    @settings_check_count       = 0
    @settings_status = { :query => true, :config => true, :dsb => true, :data_sync => true }
    
    @monitor_timer_last_good_settings_check = 0
  end
  
  #----------------------------------------------------------------#
  # Poll a collector for its current time zone
  #----------------------------------------------------------------# 
  def check_time_zones
    return if @netmri_mode != 'master' || @is_automation_grid

    # check each probe for time zone configuration 
    my_grid_member_id = @my_grid_member_id.to_s.to_sym
    @grid_members.each do |unit_id, probe|
      begin
        next if unit_id == my_grid_member_id        # skip myself (member)
        unless probe[:connectivity]
          @settings_status[:data_sync] = false
          log('error', "Failed to connect probe #{probe[:unit_ip]} for Time Zone Check")
          next if @data_sync_meta.nil?
          @data_sync_meta["ProbeStatus"][unit_id] = { "UnitIP" => probe[:unit_ip], "Status" => "failed", "Description" => "Failed to connect probe" }
          next
        end
        # Compare Time Zones
        if (probe[:time_zone] != ServerCfg.get_file_value(:TimeZone)) # Time Zones don't match.
          trace('check_time_zones', "#{probe[:unit_ip]} Time Zone [#{probe[:time_zone]}] is not in sync, should be set to [#{ServerCfg.get_file_value(:TimeZone)}]")

          #  Make API Call to collector, telling it what Time Zone it should be set to.
          begin
            response = self.post_form("http://#{probe[:unit_ip]}:#{@api_port}/api/2.10/set_time_zone",
                                      {'time_zone' => "#{ServerCfg.get_file_value(:TimeZone)}"},
                                      @api_user,
                                      @api_token
                                     )
            response_body = JSON.parse(response.body, :symbolize_names => true)
            trace('check_time_zones', "Result of attempting to set Time Zone on #{probe[:unit_ip]}: #{response_body[:message]}")
          rescue
            log('error', "Failed to update Time Zone on #{probe[:unit_ip]} due to #{$!.message}")
          end
        else # Time Zones match.
          trace('check_time_zones', "#{probe[:unit_ip]} Time Zone is in sync.")
        end
      end
    end
  end

end #end class Monitor


#-------------------------------------------------------------------------------#
#             The 'main' app starts here
#-------------------------------------------------------------------------------#
debug=false
proc_name = "settingsd"
pid_file = "/var/run/netmri/#{proc_name}.pid"
debug = false
test = false
stop = false
kill = false
detach = false
profile = false
vrfsync = false

ARGV.each do |a|
  debug = true if (a == "-d")
  detach = true if (a == "-f")
  test = true if (a == "-t")
  stop = true if (a =~ /^-?(stop|shutdown)$/i)
  kill = true if (a =~ /^-?kill$/i)
  profile = true if (a == "-p")
  vrfsync=true if (a == "-vrfsync")
end

#process kill
if kill
  Util::killall(proc_name)
  begin
    File.delete?(pid_file)
  rescue
  end
  exit 0
end

#process stop
if stop
  pid = Util::checkPidFile(pid_file)
  unless pid.nil? || !Util::same_command(pid,/settingsd/)
    Process.kill :SIGUSR1, pid
  else
    Util::killall(proc_name)
    begin
      File.delete?(pid_file)
    rescue
    end
  end
  exit 0
end

if vrfsync
  @last_vrf_sync = Time.now
  monitor = Monitor.new('settingsdManualVrfSync', false, true, false)
  monitor.query_settings
  puts "#{monitor.inspect}\n"
  #process vrf update in probes
  monitor.process_vrf()
  exit 0
end

unless Util::runAsNetMRIUser
  puts "Unable to change privilege before run. Exit."
  exit 1
end

Util::daemonize() if detach;
$0 = proc_name
# This will exit if another pid running
Util::createPidFile(pid_file, "settingsd")

monitor = Monitor.new(proc_name, detach, debug, test)
monitor.start()
exit 0

['USR1', 'USR2', 'TERM', 'QUIT', 'INT'].each do |sig|
  Signal.trap sig do
    pid = Util::checkPidFile(pid_file)
    unless pid.nil?
      Process.kill :SIGUSR1, pid
    else
      Util::killall(proc_name)
      begin
        File.delete?(pid_file)
      rescue
      end
    end
    exit 0
  end
end
at_exit {
  File.delete(pid_file)
  monitor.logger.info "Shut down"  
}
